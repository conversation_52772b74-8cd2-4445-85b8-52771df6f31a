<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

try {
    // جلب عدد الإشعارات غير المقروءة
    $unread_query = "
        SELECT COUNT(*) as unread_count
        FROM admin_notifications
        WHERE client_id = ? AND is_read = FALSE AND is_dismissed = FALSE
    ";
    
    $stmt = $pdo->prepare($unread_query);
    $stmt->execute([$client_id]);
    $unread_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب الإشعارات الجديدة (آخر 5 دقائق)
    $new_notifications_query = "
        SELECT notification_id, title, message, priority, notification_type, created_at
        FROM admin_notifications
        WHERE client_id = ? AND is_dismissed = FALSE 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ORDER BY created_at DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($new_notifications_query);
    $stmt->execute([$client_id]);
    $new_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب الإشعارات العاجلة غير المقروءة
    $urgent_query = "
        SELECT COUNT(*) as urgent_count
        FROM admin_notifications
        WHERE client_id = ? AND is_read = FALSE AND is_dismissed = FALSE 
        AND priority = 'urgent'
    ";
    
    $stmt = $pdo->prepare($urgent_query);
    $stmt->execute([$client_id]);
    $urgent_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إعداد البيانات للإرسال
    $response = [
        'success' => true,
        'unread_count' => (int)$unread_data['unread_count'],
        'urgent_count' => (int)$urgent_data['urgent_count'],
        'new_count' => count($new_notifications),
        'new_notifications' => $new_notifications,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($response, JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
