<?php
/**
 * ملف اختبار للقائمة المنسدلة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

$page_title = "اختبار القائمة المنسدلة";
$active_page = "test";

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">اختبار القائمة المنسدلة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>معلومات الجلسة:</h6>
                        <ul>
                            <li><strong>نوع المستخدم:</strong> <?php echo $is_employee ? 'موظف' : 'مالك المحل'; ?></li>
                            <li><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($user_name); ?></li>
                            <li><strong>اسم المحل:</strong> <?php echo htmlspecialchars($business_name); ?></li>
                            <?php if ($is_employee): ?>
                                <li><strong>دور الموظف:</strong> <?php echo htmlspecialchars($user_role); ?></li>
                                <li><strong>معرف الموظف:</strong> <?php echo $_SESSION['employee_id']; ?></li>
                            <?php endif; ?>
                            <li><strong>معرف العميل:</strong> <?php echo $_SESSION['client_id']; ?></li>
                        </ul>
                    </div>

                    <div class="alert alert-success">
                        <h6>اختبار الصلاحيات:</h6>
                        <ul>
                            <li><strong>manage_settings:</strong> <?php echo hasPermission('manage_settings') ? 'نعم' : 'لا'; ?></li>
                            <li><strong>هل هو عميل:</strong> <?php echo $is_client ? 'نعم' : 'لا'; ?></li>
                            <li><strong>هل هو موظف:</strong> <?php echo $is_employee ? 'نعم' : 'لا'; ?></li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6>اختبار الروابط:</h6>
                        <div class="d-grid gap-2 d-md-block">
                            <a href="profile.php" class="btn btn-primary">الملف الشخصي</a>
                            <a href="settings.php" class="btn btn-success">إعدادات المحل</a>
                            <a href="dashboard.php" class="btn btn-secondary">لوحة التحكم</a>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>تشخيص JavaScript:</h6>
                        <div id="jsTest" class="alert alert-secondary">
                            جاري التحقق من JavaScript...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const testDiv = document.getElementById('jsTest');
    
    // اختبار Bootstrap
    const bootstrapTest = typeof bootstrap !== 'undefined';
    
    // اختبار عناصر القائمة المنسدلة
    const dropdownToggle = document.querySelector('#userDropdown');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    
    let testResults = [];
    testResults.push('Bootstrap محمل: ' + (bootstrapTest ? 'نعم' : 'لا'));
    testResults.push('عنصر التبديل موجود: ' + (dropdownToggle ? 'نعم' : 'لا'));
    testResults.push('القائمة المنسدلة موجودة: ' + (dropdownMenu ? 'نعم' : 'لا'));
    
    if (dropdownToggle) {
        testResults.push('data-bs-toggle: ' + dropdownToggle.getAttribute('data-bs-toggle'));
        testResults.push('aria-expanded: ' + dropdownToggle.getAttribute('aria-expanded'));
    }
    
    testDiv.innerHTML = testResults.join('<br>');
    testDiv.className = 'alert alert-info';
    
    // اختبار النقر على القائمة المنسدلة
    if (dropdownToggle) {
        dropdownToggle.addEventListener('click', function() {
            console.log('تم النقر على القائمة المنسدلة');
            testDiv.innerHTML += '<br><strong>تم النقر على القائمة المنسدلة!</strong>';
        });
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
