<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من وجود معرف الملخص
if (!isset($_GET['summary_id']) || !is_numeric($_GET['summary_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الملخص مطلوب']);
    exit;
}

$summary_id = (int)$_GET['summary_id'];

try {
    // جلب تفاصيل الملخص
    $summary_query = "
        SELECT s.*, ss.*,
               supervisor.name as supervisor_name,
               generator.name as generated_by_name,
               CASE 
                   WHEN ss.performance_score >= 8.0 THEN 'ممتاز'
                   WHEN ss.performance_score >= 6.0 THEN 'جيد'
                   WHEN ss.performance_score >= 4.0 THEN 'مقبول'
                   ELSE 'ضعيف'
               END as performance_grade
        FROM shift_summaries ss
        JOIN shifts s ON ss.shift_id = s.shift_id
        LEFT JOIN employees supervisor ON s.shift_supervisor = supervisor.id
        LEFT JOIN employees generator ON ss.generated_by = generator.id
        WHERE ss.summary_id = ? AND s.client_id = ?
    ";

    $stmt = $pdo->prepare($summary_query);
    $stmt->execute([$summary_id, $client_id]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$summary) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الملخص غير موجود']);
        exit;
    }

    // جلب تفاصيل الموظفين في الشيفت
    $employees_query = "
        SELECT e.name, e.role, sa.check_in_time, sa.check_out_time, 
               sa.actual_hours, sa.overtime_hours, sa.status,
               sa.late_minutes, sa.early_leave_minutes,
               sep.total_score, sep.attendance_score, sep.punctuality_score, 
               sep.performance_score as employee_performance_score
        FROM employee_shifts es
        JOIN employees e ON es.employee_id = e.id
        LEFT JOIN shift_attendance sa ON es.assignment_id = sa.assignment_id
        LEFT JOIN shift_employee_performance sep ON es.shift_id = sep.shift_id AND es.employee_id = sep.employee_id
        WHERE es.shift_id = ?
        ORDER BY e.name
    ";

    $stmt = $pdo->prepare($employees_query);
    $stmt->execute([$summary['shift_id']]);
    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الأحداث المهمة في الشيفت
    $events_query = "
        SELECT event_type, event_title, event_description, severity, 
               recorded_at, is_resolved, e.name as employee_name
        FROM shift_events se
        LEFT JOIN employees e ON se.employee_id = e.id
        WHERE se.shift_id = ? AND se.severity IN ('high', 'critical')
        ORDER BY se.recorded_at DESC
        LIMIT 10
    ";

    $stmt = $pdo->prepare($events_query);
    $stmt->execute([$summary['shift_id']]);
    $important_events = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب المهام المكتملة
    $tasks_query = "
        SELECT task_title, task_description, e.name as assigned_to_name,
               completed_at, actual_duration, completion_notes
        FROM shift_tasks st
        LEFT JOIN employees e ON st.assigned_to = e.id
        WHERE st.shift_id = ? AND st.task_status = 'completed'
        ORDER BY st.completed_at DESC
    ";

    $stmt = $pdo->prepare($tasks_query);
    $stmt->execute([$summary['shift_id']]);
    $completed_tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إعداد البيانات للإرسال
    $response_data = [
        'success' => true,
        'summary' => $summary,
        'employees' => $employees,
        'important_events' => $important_events,
        'completed_tasks' => $completed_tasks,
        'statistics' => [
            'total_employees' => count($employees),
            'present_employees' => count(array_filter($employees, function($emp) {
                return !empty($emp['check_in_time']);
            })),
            'on_time_employees' => count(array_filter($employees, function($emp) {
                return $emp['late_minutes'] == 0;
            })),
            'overtime_employees' => count(array_filter($employees, function($emp) {
                return $emp['overtime_hours'] > 0;
            })),
            'total_work_hours' => array_sum(array_column($employees, 'actual_hours')),
            'total_overtime_hours' => array_sum(array_column($employees, 'overtime_hours')),
            'average_performance' => count($employees) > 0 ? 
                array_sum(array_column($employees, 'total_score')) / count(array_filter($employees, function($emp) {
                    return !empty($emp['total_score']);
                })) : 0
        ]
    ];

    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($response_data, JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
