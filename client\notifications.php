<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$current_employee_id = $_SESSION['employee_id'] ?? null;

$page_title = "الإشعارات";
$active_page = "notifications";

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'mark_read':
                    $stmt = $pdo->prepare("
                        UPDATE admin_notifications 
                        SET is_read = TRUE, read_at = CURRENT_TIMESTAMP 
                        WHERE notification_id = ? AND client_id = ?
                    ");
                    $stmt->execute([$_POST['notification_id'], $client_id]);
                    break;

                case 'mark_all_read':
                    $stmt = $pdo->prepare("
                        UPDATE admin_notifications 
                        SET is_read = TRUE, read_at = CURRENT_TIMESTAMP 
                        WHERE client_id = ? AND is_read = FALSE
                    ");
                    $stmt->execute([$client_id]);
                    $_SESSION['success'] = "تم تمييز جميع الإشعارات كمقروءة";
                    break;

                case 'dismiss':
                    $stmt = $pdo->prepare("
                        UPDATE admin_notifications 
                        SET is_dismissed = TRUE, dismissed_at = CURRENT_TIMESTAMP 
                        WHERE notification_id = ? AND client_id = ?
                    ");
                    $stmt->execute([$_POST['notification_id'], $client_id]);
                    break;

                case 'delete':
                    $stmt = $pdo->prepare("
                        DELETE FROM admin_notifications 
                        WHERE notification_id = ? AND client_id = ?
                    ");
                    $stmt->execute([$_POST['notification_id'], $client_id]);
                    $_SESSION['success'] = "تم حذف الإشعار";
                    break;

                case 'clear_old':
                    $stmt = $pdo->prepare("
                        DELETE FROM admin_notifications 
                        WHERE client_id = ? AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
                    ");
                    $stmt->execute([$client_id]);
                    $_SESSION['success'] = "تم حذف الإشعارات القديمة";
                    break;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
    
    header('Location: notifications.php');
    exit;
}

// الفلاتر
$filter_type = $_GET['type'] ?? '';
$filter_priority = $_GET['priority'] ?? '';
$show_read = $_GET['show_read'] ?? '1';
$show_dismissed = $_GET['show_dismissed'] ?? '0';

// بناء الاستعلام
$where_conditions = ["client_id = ?"];
$params = [$client_id];

if ($filter_type) {
    $where_conditions[] = "notification_type = ?";
    $params[] = $filter_type;
}

if ($filter_priority) {
    $where_conditions[] = "priority = ?";
    $params[] = $filter_priority;
}

if ($show_read === '0') {
    $where_conditions[] = "is_read = FALSE";
}

if ($show_dismissed === '0') {
    $where_conditions[] = "is_dismissed = FALSE";
}

$where_clause = implode(' AND ', $where_conditions);

// جلب الإشعارات
$notifications_query = "
    SELECT * FROM admin_notifications
    WHERE $where_clause
    ORDER BY 
        CASE priority 
            WHEN 'urgent' THEN 1 
            WHEN 'high' THEN 2 
            WHEN 'medium' THEN 3 
            ELSE 4 
        END,
        created_at DESC
    LIMIT 100
";

$notifications = $pdo->prepare($notifications_query);
$notifications->execute($params);
$notifications_data = $notifications->fetchAll(PDO::FETCH_ASSOC);

// إحصائيات الإشعارات
$stats_query = "
    SELECT 
        COUNT(*) as total_notifications,
        COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread_count,
        COUNT(CASE WHEN priority = 'urgent' THEN 1 END) as urgent_count,
        COUNT(CASE WHEN priority = 'high' THEN 1 END) as high_count,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_count
    FROM admin_notifications
    WHERE client_id = ? AND is_dismissed = FALSE
";

$stats = $pdo->prepare($stats_query);
$stats->execute([$client_id]);
$stats_data = $stats->fetch(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-bell me-2"></i><?php echo $page_title; ?>
                    <?php if ($stats_data['unread_count'] > 0): ?>
                        <span class="badge bg-danger"><?php echo $stats_data['unread_count']; ?></span>
                    <?php endif; ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="mark_all_read">
                            <button type="submit" class="btn btn-outline-primary" <?php echo $stats_data['unread_count'] == 0 ? 'disabled' : ''; ?>>
                                <i class="fas fa-check-double me-1"></i>تمييز الكل كمقروء
                            </button>
                        </form>
                    </div>
                    <div class="btn-group">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="clear_old">
                            <button type="submit" class="btn btn-outline-secondary" onclick="return confirm('هل تريد حذف الإشعارات الأقدم من 30 يوم؟')">
                                <i class="fas fa-trash me-1"></i>حذف القديمة
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['total_notifications']; ?></h4>
                            <p class="card-text">إجمالي الإشعارات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['unread_count']; ?></h4>
                            <p class="card-text">غير مقروءة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-danger">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['urgent_count']; ?></h4>
                            <p class="card-text">عاجلة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['today_count']; ?></h4>
                            <p class="card-text">اليوم</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">فلاتر الإشعارات</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">جميع الأنواع</option>
                                <option value="shift_completed" <?php echo $filter_type == 'shift_completed' ? 'selected' : ''; ?>>اكتمال شيفت</option>
                                <option value="shift_summary_ready" <?php echo $filter_type == 'shift_summary_ready' ? 'selected' : ''; ?>>تقرير جاهز</option>
                                <option value="critical_event" <?php echo $filter_type == 'critical_event' ? 'selected' : ''; ?>>حدث حرج</option>
                                <option value="unresolved_issues" <?php echo $filter_type == 'unresolved_issues' ? 'selected' : ''; ?>>مشاكل غير محلولة</option>
                                <option value="attendance_alert" <?php echo $filter_type == 'attendance_alert' ? 'selected' : ''; ?>>تنبيه حضور</option>
                                <option value="performance_alert" <?php echo $filter_type == 'performance_alert' ? 'selected' : ''; ?>>تنبيه أداء</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="">جميع الأولويات</option>
                                <option value="urgent" <?php echo $filter_priority == 'urgent' ? 'selected' : ''; ?>>عاجل</option>
                                <option value="high" <?php echo $filter_priority == 'high' ? 'selected' : ''; ?>>عالي</option>
                                <option value="medium" <?php echo $filter_priority == 'medium' ? 'selected' : ''; ?>>متوسط</option>
                                <option value="low" <?php echo $filter_priority == 'low' ? 'selected' : ''; ?>>منخفض</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_read" name="show_read" value="1" <?php echo $show_read == '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_read">
                                    إظهار المقروءة
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_dismissed" name="show_dismissed" value="1" <?php echo $show_dismissed == '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_dismissed">
                                    إظهار المرفوضة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="notifications.php" class="btn btn-secondary">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الإشعارات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإشعارات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($notifications_data)): ?>
                        <div class="alert alert-info text-center">
                            <i class="fas fa-bell-slash fa-3x mb-3"></i>
                            <h5>لا توجد إشعارات</h5>
                            <p class="mb-0">لا توجد إشعارات تطابق المعايير المحددة</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($notifications_data as $notification): ?>
                                <?php
                                $priority_classes = [
                                    'urgent' => 'border-danger',
                                    'high' => 'border-warning',
                                    'medium' => 'border-info',
                                    'low' => 'border-secondary'
                                ];
                                
                                $priority_icons = [
                                    'urgent' => 'fas fa-exclamation-triangle text-danger',
                                    'high' => 'fas fa-exclamation-circle text-warning',
                                    'medium' => 'fas fa-info-circle text-info',
                                    'low' => 'fas fa-circle text-secondary'
                                ];

                                $type_labels = [
                                    'shift_completed' => 'اكتمال شيفت',
                                    'shift_summary_ready' => 'تقرير جاهز',
                                    'critical_event' => 'حدث حرج',
                                    'unresolved_issues' => 'مشاكل غير محلولة',
                                    'attendance_alert' => 'تنبيه حضور',
                                    'performance_alert' => 'تنبيه أداء',
                                    'system_alert' => 'تنبيه نظام',
                                    'maintenance_reminder' => 'تذكير صيانة',
                                    'other' => 'أخرى'
                                ];
                                ?>
                                <div class="list-group-item <?php echo $priority_classes[$notification['priority']] ?? ''; ?> <?php echo !$notification['is_read'] ? 'bg-light' : ''; ?> <?php echo $notification['is_dismissed'] ? 'opacity-50' : ''; ?>">
                                    <div class="d-flex w-100 justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="<?php echo $priority_icons[$notification['priority']] ?? 'fas fa-circle'; ?> me-2"></i>
                                                <h6 class="mb-0 me-2"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                                <span class="badge bg-secondary"><?php echo $type_labels[$notification['notification_type']] ?? $notification['notification_type']; ?></span>
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="badge bg-primary ms-2">جديد</span>
                                                <?php endif; ?>
                                                <?php if ($notification['is_dismissed']): ?>
                                                    <span class="badge bg-secondary ms-2">مرفوض</span>
                                                <?php endif; ?>
                                            </div>
                                            <p class="mb-2"><?php echo htmlspecialchars($notification['message']); ?></p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                                                <?php if ($notification['read_at']): ?>
                                                    | <i class="fas fa-eye me-1"></i>قُرئ في: <?php echo date('H:i', strtotime($notification['read_at'])); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <div class="btn-group-vertical btn-group-sm ms-3" role="group">
                                            <?php if ($notification['action_url']): ?>
                                                <a href="<?php echo htmlspecialchars($notification['action_url']); ?>" class="btn btn-outline-primary btn-sm" onclick="markAsRead(<?php echo $notification['notification_id']; ?>)">
                                                    <i class="fas fa-external-link-alt me-1"></i><?php echo htmlspecialchars($notification['action_label'] ?? 'عرض'); ?>
                                                </a>
                                            <?php endif; ?>
                                            <?php if (!$notification['is_read']): ?>
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="markAsRead(<?php echo $notification['notification_id']; ?>)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                            <?php if (!$notification['is_dismissed']): ?>
                                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="dismissNotification(<?php echo $notification['notification_id']; ?>)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteNotification(<?php echo $notification['notification_id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تمييز الإشعار كمقروء
function markAsRead(notificationId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="mark_read">
        <input type="hidden" name="notification_id" value="${notificationId}">
    `;
    document.body.appendChild(form);
    form.submit();
}

// رفض الإشعار
function dismissNotification(notificationId) {
    if (confirm('هل تريد رفض هذا الإشعار؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="dismiss">
            <input type="hidden" name="notification_id" value="${notificationId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// حذف الإشعار
function deleteNotification(notificationId) {
    if (confirm('هل تريد حذف هذا الإشعار نهائياً؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="notification_id" value="${notificationId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// تحديث تلقائي للإشعارات كل دقيقة
setInterval(function() {
    // يمكن إضافة تحديث AJAX هنا لجلب الإشعارات الجديدة
    console.log('تحديث تلقائي للإشعارات...');
}, 60000);

// إضافة صوت للإشعارات الجديدة (اختياري)
function playNotificationSound() {
    // يمكن إضافة ملف صوتي للإشعارات
    try {
        const audio = new Audio('assets/sounds/notification.mp3');
        audio.play().catch(e => console.log('لا يمكن تشغيل الصوت'));
    } catch (e) {
        console.log('ملف الصوت غير متوفر');
    }
}

// فحص الإشعارات الجديدة عبر AJAX
function checkNewNotifications() {
    fetch('api/check_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.new_count > 0) {
                // تحديث عداد الإشعارات في الشريط العلوي
                updateNotificationBadge(data.new_count);

                // تشغيل صوت الإشعار
                playNotificationSound();

                // إظهار إشعار في المتصفح (إذا كان مسموحاً)
                if (Notification.permission === 'granted') {
                    new Notification('إشعار جديد', {
                        body: `لديك ${data.new_count} إشعار جديد`,
                        icon: 'assets/images/notification-icon.png'
                    });
                }
            }
        })
        .catch(error => console.error('خطأ في فحص الإشعارات:', error));
}

// تحديث عداد الإشعارات
function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

// طلب إذن الإشعارات من المتصفح
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}

// فحص الإشعارات الجديدة كل 30 ثانية
setInterval(checkNewNotifications, 30000);

// فحص عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkNewNotifications();
});
</script>

<?php include 'includes/footer.php'; ?>
