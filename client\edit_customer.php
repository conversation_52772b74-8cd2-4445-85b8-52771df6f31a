<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "تعديل بيانات العميل";
$active_page = "customers";

// التحقق من معرف العميل
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($customer_id <= 0) {
    $_SESSION['error'] = 'معرف العميل غير صحيح';
    header('Location: customers.php');
    exit;
}

// جلب بيانات العميل
try {
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE customer_id = ? AND client_id = ?");
    $stmt->execute([$customer_id, $client_id]);
    $customer = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$customer) {
        $_SESSION['error'] = 'العميل غير موجود';
        header('Location: customers.php');
        exit;
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'خطأ في جلب بيانات العميل';
    header('Location: customers.php');
    exit;
}

// تحديث بيانات العميل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $name = trim($_POST['name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $notes = trim($_POST['notes'] ?? '');

        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception('اسم العميل مطلوب');
        }

        if (empty($phone)) {
            throw new Exception('رقم الهاتف مطلوب');
        }

        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }

        // التحقق من عدم تكرار رقم الهاتف (باستثناء العميل الحالي)
        $check_phone = $pdo->prepare("SELECT customer_id FROM customers WHERE phone = ? AND client_id = ? AND customer_id != ?");
        $check_phone->execute([$phone, $client_id, $customer_id]);
        if ($check_phone->rowCount() > 0) {
            throw new Exception('رقم الهاتف موجود مسبقاً لعميل آخر');
        }

        // التحقق من عدم تكرار البريد الإلكتروني (باستثناء العميل الحالي)
        if (!empty($email)) {
            $check_email = $pdo->prepare("SELECT customer_id FROM customers WHERE email = ? AND client_id = ? AND customer_id != ?");
            $check_email->execute([$email, $client_id, $customer_id]);
            if ($check_email->rowCount() > 0) {
                throw new Exception('البريد الإلكتروني موجود مسبقاً لعميل آخر');
            }
        }

        // تحديث بيانات العميل
        $stmt = $pdo->prepare("
            UPDATE customers 
            SET name = ?, phone = ?, email = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
            WHERE customer_id = ? AND client_id = ?
        ");

        $result = $stmt->execute([
            $name,
            $phone,
            $email ?: null,
            $notes ?: null,
            $customer_id,
            $client_id
        ]);

        if ($result) {
            $_SESSION['success'] = "تم تحديث بيانات العميل بنجاح";
            header('Location: customers.php');
            exit;
        } else {
            throw new Exception('فشل في تحديث بيانات العميل');
        }

    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    } catch (PDOException $e) {
        $_SESSION['error'] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        error_log('خطأ PDO في تحديث العميل: ' . $e->getMessage());
    }
}

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>تعديل بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" 
                                           value="<?php echo htmlspecialchars($customer['name']); ?>" 
                                           required minlength="2" maxlength="255">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" name="phone" 
                                           value="<?php echo htmlspecialchars($customer['phone']); ?>" 
                                           required pattern="[0-9+\-\s]+" minlength="10" maxlength="20">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" 
                                   value="<?php echo htmlspecialchars($customer['email'] ?? ''); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="4" 
                                      maxlength="1000"><?php echo htmlspecialchars($customer['notes'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                تاريخ التسجيل: <?php echo date('Y-m-d H:i', strtotime($customer['created_at'])); ?>
                            </small>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ التغييرات
                            </button>
                            <a href="customers.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة
                            </a>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash me-1"></i>حذف العميل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // إرسال طلب حذف
            window.location.href = `delete_customer.php?id=<?php echo $customer_id; ?>`;
        }
    });
}

// التحقق من صحة النموذج
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        const name = form.querySelector('input[name="name"]').value.trim();
        const phone = form.querySelector('input[name="phone"]').value.trim();
        const email = form.querySelector('input[name="email"]').value.trim();
        
        if (name.length < 2) {
            e.preventDefault();
            Swal.fire('خطأ', 'اسم العميل يجب أن يكون على الأقل حرفين', 'error');
            return;
        }
        
        if (!/^[0-9+\-\s]{10,20}$/.test(phone)) {
            e.preventDefault();
            Swal.fire('خطأ', 'رقم الهاتف غير صحيح', 'error');
            return;
        }
        
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            e.preventDefault();
            Swal.fire('خطأ', 'البريد الإلكتروني غير صحيح', 'error');
            return;
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
