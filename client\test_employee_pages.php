<?php
/**
 * اختبار نظام صفحات الموظفين
 * يتحقق من إمكانية تحديد الصفحات لكل موظف
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار نظام صفحات الموظفين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='card shadow'>
        <div class='card-header bg-success text-white'>
            <h3 class='mb-0'><i class='fas fa-file-alt me-2'></i>اختبار نظام صفحات الموظفين</h3>
        </div>
        <div class='card-body'>";

try {
    // فحص جدول الصفحات
    echo "<h5><i class='fas fa-database me-2'></i>فحص جدول الصفحات</h5>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pages WHERE is_active = 1");
    $pages_count = $stmt->fetch()['count'];
    
    if ($pages_count > 0) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>يوجد $pages_count صفحة متاحة في النظام</p>";
        
        // عرض الصفحات حسب الفئة
        $stmt = $pdo->query("
            SELECT category, COUNT(*) as count, 
                   GROUP_CONCAT(page_label SEPARATOR ', ') as pages
            FROM pages 
            WHERE is_active = 1 
            GROUP BY category
        ");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='table-responsive'>
                <table class='table table-striped'>
                    <thead>
                        <tr>
                            <th>الفئة</th>
                            <th>عدد الصفحات</th>
                            <th>الصفحات</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($categories as $cat) {
            echo "<tr>
                    <td><strong>{$cat['category']}</strong></td>
                    <td><span class='badge bg-primary'>{$cat['count']}</span></td>
                    <td><small>{$cat['pages']}</small></td>
                  </tr>";
        }
        
        echo "    </tbody>
                </table>
              </div>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>لا توجد صفحات في النظام</p>";
        echo "<div class='alert alert-warning'>
                <p>يجب تشغيل ملف SQL لإضافة الصفحات الأساسية</p>
                <a href='setup_permissions_quick.php' class='btn btn-warning'>إعداد النظام</a>
              </div>";
    }
    
    // فحص صفحات الموظفين
    echo "<h5><i class='fas fa-users me-2'></i>فحص صفحات الموظفين</h5>";
    
    $stmt = $pdo->query("
        SELECT e.id, e.name, e.role, e.custom_permissions,
               COUNT(ep.page_id) as assigned_pages
        FROM employees e
        LEFT JOIN employee_pages ep ON e.id = ep.employee_id
        GROUP BY e.id, e.name, e.role, e.custom_permissions
        ORDER BY e.name
    ");
    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($employees) > 0) {
        echo "<div class='table-responsive'>
                <table class='table table-striped'>
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>الدور</th>
                            <th>صلاحيات مخصصة</th>
                            <th>الصفحات المخصصة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($employees as $emp) {
            $custom_badge = $emp['custom_permissions'] ? 
                '<span class="badge bg-success">نعم</span>' : 
                '<span class="badge bg-secondary">لا</span>';
            
            $pages_info = $emp['custom_permissions'] ? 
                "<span class='badge bg-info'>{$emp['assigned_pages']} صفحة</span>" : 
                "<span class='text-muted'>حسب الدور</span>";
            
            echo "<tr>
                    <td><strong>{$emp['name']}</strong></td>
                    <td>{$emp['role']}</td>
                    <td>$custom_badge</td>
                    <td>$pages_info</td>
                    <td>
                        <a href='edit_employee.php?id={$emp['id']}' class='btn btn-sm btn-primary' target='_blank'>
                            <i class='fas fa-edit me-1'></i>تعديل
                        </a>
                        <button class='btn btn-sm btn-info' onclick='showEmployeePages({$emp['id']})'>
                            <i class='fas fa-eye me-1'></i>عرض الصفحات
                        </button>
                    </td>
                  </tr>";
        }
        
        echo "    </tbody>
                </table>
              </div>";
    } else {
        echo "<p class='warning'><i class='fas fa-exclamation-triangle me-2'></i>لا يوجد موظفين في النظام</p>";
    }
    
    // إحصائيات سريعة
    echo "<h5><i class='fas fa-chart-bar me-2'></i>إحصائيات سريعة</h5>";
    
    $stats = [];
    
    // عدد الموظفين الذين يستخدمون صلاحيات مخصصة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees WHERE custom_permissions = 1");
    $stats['custom_employees'] = $stmt->fetch()['count'];
    
    // عدد الموظفين الذين لديهم صفحات مخصصة
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT employee_id) as count 
        FROM employee_pages
    ");
    $stats['employees_with_pages'] = $stmt->fetch()['count'];
    
    // متوسط عدد الصفحات لكل موظف
    $stmt = $pdo->query("
        SELECT AVG(page_count) as avg_pages
        FROM (
            SELECT employee_id, COUNT(*) as page_count
            FROM employee_pages
            GROUP BY employee_id
        ) as page_counts
    ");
    $avg_result = $stmt->fetch();
    $stats['avg_pages'] = $avg_result['avg_pages'] ? round($avg_result['avg_pages'], 1) : 0;
    
    echo "<div class='row'>
            <div class='col-md-4'>
                <div class='card border-primary'>
                    <div class='card-body text-center'>
                        <h5 class='card-title text-primary'>{$stats['custom_employees']}</h5>
                        <p class='card-text'>موظف يستخدم صلاحيات مخصصة</p>
                    </div>
                </div>
            </div>
            <div class='col-md-4'>
                <div class='card border-success'>
                    <div class='card-body text-center'>
                        <h5 class='card-title text-success'>{$stats['employees_with_pages']}</h5>
                        <p class='card-text'>موظف لديه صفحات مخصصة</p>
                    </div>
                </div>
            </div>
            <div class='col-md-4'>
                <div class='card border-info'>
                    <div class='card-body text-center'>
                        <h5 class='card-title text-info'>{$stats['avg_pages']}</h5>
                        <p class='card-text'>متوسط الصفحات لكل موظف</p>
                    </div>
                </div>
            </div>
          </div>";
    
    // اختبار الدوال
    echo "<h5><i class='fas fa-code me-2'></i>اختبار دوال النظام</h5>";
    
    require_once 'includes/employee-auth.php';
    
    if (function_exists('employeeCanAccessPage')) {
        echo "<p class='success'><i class='fas fa-check me-2'></i>دالة employeeCanAccessPage موجودة</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times me-2'></i>دالة employeeCanAccessPage غير موجودة</p>";
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h6><i class='fas fa-check-circle me-2'></i>النظام جاهز!</h6>
            <p>يمكنك الآن تحديد الصفحات التي يستطيع كل موظف الوصول إليها من صفحة تعديل الموظف.</p>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h6><i class='fas fa-exclamation-circle me-2'></i>خطأ في قاعدة البيانات</h6>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "        </div>
        <div class='card-footer text-center'>
            <a href='employees.php' class='btn btn-primary me-2'>
                <i class='fas fa-users me-2'></i>إدارة الموظفين
            </a>
            <a href='edit_employee.php?id=1' class='btn btn-success me-2' target='_blank'>
                <i class='fas fa-edit me-2'></i>اختبار التعديل
            </a>
            <a href='setup_permissions_quick.php' class='btn btn-warning'>
                <i class='fas fa-cogs me-2'></i>إعداد النظام
            </a>
        </div>
    </div>
</div>

<script>
function showEmployeePages(employeeId) {
    // يمكن إضافة نافذة منبثقة لعرض صفحات الموظف
    alert('سيتم عرض صفحات الموظف رقم: ' + employeeId);
}
</script>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
