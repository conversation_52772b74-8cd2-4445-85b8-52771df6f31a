<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة الكافتيريا
    if (!hasPagePermission('cafeteria')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة الكافتيريا';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين - التحقق من الوصول للصفحة والصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('cafeteria')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_cafeteria')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// Initialize variables
$page_title = "إدارة الكافتيريا";
$active_page = "cafeteria";
$messages = [];

// Handle all POST/GET operations before any output
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['delete'])) {
    // إضافة منتج جديد
    if (isset($_POST['add_item'])) {
        try {
            $name = trim($_POST['name']);
            $category = trim($_POST['category']);

            // التحقق من عدم وجود منتج بنفس الاسم والتصنيف للعميل
            $check_duplicate = $pdo->prepare('SELECT id FROM cafeteria_items WHERE name = ? AND category = ? AND client_id = ?');
            $check_duplicate->execute([$name, $category, $client_id]);

            if ($check_duplicate->rowCount() > 0) {
                $messages['error'] = "يوجد منتج بنفس الاسم والتصنيف مسبقاً";
            } else {
                $stmt = $pdo->prepare('INSERT INTO cafeteria_items (name, price, category, description, client_id)
                    VALUES (:name, :price, :category, :description, :client_id)');
                $stmt->execute([
                    'name' => $name,
                    'price' => floatval($_POST['price']),
                    'category' => $category,
                    'description' => trim($_POST['description']),
                    'client_id' => $client_id
                ]);
                $_SESSION['success'] = "تم إضافة المنتج بنجاح";
                header('Location: cafeteria.php');
                exit;
            }
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء إضافة المنتج";
        }
    }

    // تعديل منتج
    if (isset($_POST['edit_item'])) {
        try {
            $name = trim($_POST['name']);
            $category = trim($_POST['category']);
            $item_id = intval($_POST['item_id']);

            // التحقق من عدم وجود منتج آخر بنفس الاسم والتصنيف للعميل (باستثناء المنتج الحالي)
            $check_duplicate = $pdo->prepare('SELECT id FROM cafeteria_items WHERE name = ? AND category = ? AND client_id = ? AND id != ?');
            $check_duplicate->execute([$name, $category, $client_id, $item_id]);

            if ($check_duplicate->rowCount() > 0) {
                $messages['error'] = "يوجد منتج آخر بنفس الاسم والتصنيف مسبقاً";
            } else {
                $stmt = $pdo->prepare('UPDATE cafeteria_items SET
                    name = :name,
                    price = :price,
                    category = :category,
                    description = :description
                    WHERE id = :id AND client_id = :client_id');

                $stmt->execute([
                    'name' => $name,
                    'price' => floatval($_POST['price']),
                    'category' => $category,
                    'description' => trim($_POST['description']),
                    'id' => $item_id,
                    'client_id' => $client_id
                ]);
                $_SESSION['success'] = "تم تعديل المنتج بنجاح";
                header('Location: cafeteria.php');
                exit;
            }
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء تعديل المنتج";
        }
    }

    // حذف منتج
    if (isset($_GET['delete'])) {
        try {
            $stmt = $pdo->prepare('DELETE FROM cafeteria_items WHERE id = ? AND client_id = ?');
            $stmt->execute([$_GET['delete'], $client_id]);
            $_SESSION['success'] = "تم حذف المنتج بنجاح";
            header('Location: cafeteria.php');
            exit;
        } catch (PDOException $e) {
            $messages['error'] = "حدث خطأ أثناء حذف المنتج";
        }
    }

    // إضافة تصنيف جديد
    if (isset($_POST['add_category']) && !empty($_POST['category_name'])) {
        try {
            $stmt = $pdo->prepare('INSERT INTO categories (name, client_id) VALUES (?, ?)');
            $stmt->execute([trim($_POST['category_name']), $client_id]);
            $_SESSION['success'] = "تم إضافة التصنيف بنجاح";
            header('Location: cafeteria.php');
            exit;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry error
                $messages['error'] = "هذا التصنيف موجود بالفعل";
            } else {
                $messages['error'] = "حدث خطأ أثناء إضافة التصنيف: " . $e->getMessage();
                // تسجيل الخطأ للمطور
                error_log("Category add error: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());
            }
        }
    }

    // تعديل تصنيف
    if (isset($_POST['edit_category'])) {
        try {
            $stmt = $pdo->prepare('UPDATE categories SET name = ? WHERE category_id = ? AND client_id = ?');
            $stmt->execute([
                trim($_POST['category_name']),
                $_POST['category_id'],
                $client_id
            ]);
            $_SESSION['success'] = "تم تعديل التصنيف بنجاح";
            header('Location: cafeteria.php');
            exit;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                $messages['error'] = "هذا التصنيف موجود بالفعل";
            } else {
                $messages['error'] = "حدث خطأ أثناء تعديل التصنيف";
            }
        }
    }

    // حذف تصنيف
    if (isset($_GET['delete_category'])) {
        try {
            $categoryId = (int)$_GET['delete_category'];

            // التحقق من صحة معرف التصنيف
            if ($categoryId <= 0) {
                $_SESSION['error'] = "معرف التصنيف غير صحيح";
                header('Location: cafeteria.php');
                exit;
            }

            // الحصول على اسم التصنيف أولاً
            $getCategoryName = $pdo->prepare('SELECT name FROM categories WHERE category_id = ? AND client_id = ?');
            $getCategoryName->execute([$categoryId, $client_id]);
            $categoryName = $getCategoryName->fetchColumn();

            if (!$categoryName) {
                $_SESSION['error'] = "التصنيف غير موجود أو لا تملك صلاحية حذفه";
                header('Location: cafeteria.php');
                exit;
            }

            // تحقق من وجود منتجات مرتبطة بالتصنيف (بالاسم وليس بالمعرف)
            $checkProducts = $pdo->prepare('SELECT COUNT(*) FROM cafeteria_items WHERE category = ? AND client_id = ?');
            $checkProducts->execute([$categoryName, $client_id]);
            $hasProducts = $checkProducts->fetchColumn() > 0;

            if ($hasProducts) {
                // عرض عدد المنتجات المرتبطة
                $productCount = $checkProducts->fetchColumn();
                $_SESSION['error'] = "لا يمكن حذف التصنيف '$categoryName' لوجود $productCount منتج مرتبط به. يرجى حذف المنتجات أولاً أو تغيير تصنيفها.";
            } else {
                // بدء معاملة قاعدة البيانات
                $pdo->beginTransaction();

                try {
                    // تنظيف أي مراجع للتصنيف في جدول المنتجات (category_id)
                    $cleanupStmt = $pdo->prepare('UPDATE cafeteria_items SET category_id = NULL WHERE category_id = ? AND client_id = ?');
                    $cleanupStmt->execute([$categoryId, $client_id]);

                    // حذف التصنيف
                    $deleteStmt = $pdo->prepare('DELETE FROM categories WHERE category_id = ? AND client_id = ?');
                    $deleteStmt->execute([$categoryId, $client_id]);

                    if ($deleteStmt->rowCount() > 0) {
                        // تأكيد المعاملة
                        $pdo->commit();
                        $_SESSION['success'] = "تم حذف التصنيف '$categoryName' بنجاح";
                    } else {
                        // إلغاء المعاملة
                        $pdo->rollBack();
                        $_SESSION['error'] = "لم يتم العثور على التصنيف أو لا يمكن حذفه";
                    }
                } catch (PDOException $e) {
                    // إلغاء المعاملة في حالة الخطأ
                    $pdo->rollBack();
                    throw $e;
                }
            }

            header('Location: cafeteria.php');
            exit;
        } catch (PDOException $e) {
            // تسجيل الخطأ للمطور
            error_log("Category deletion error: " . $e->getMessage() . " in " . $e->getFile() . " line " . $e->getLine());

            // رسالة خطأ مفصلة للمستخدم
            $errorMessage = "حدث خطأ أثناء حذف التصنيف";
            if ($e->getCode() == 23000) {
                $errorMessage = "لا يمكن حذف التصنيف لوجود منتجات مرتبطة به";
            } elseif (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                $errorMessage = "لا يمكن حذف التصنيف بسبب قيود قاعدة البيانات. يرجى تشغيل سكريبت الإصلاح أولاً.";
            }

            $_SESSION['error'] = $errorMessage;
            header('Location: cafeteria.php');
            exit;
        }
    }
}

// Fetch data after all operations
try {
    $categories = $pdo->prepare('SELECT * FROM categories WHERE client_id = ? ORDER BY name');
    $categories->execute([$client_id]);
    $categories = $categories->fetchAll();
} catch (PDOException $e) {
    // إذا لم يكن عمود client_id موجود، جلب جميع التصنيفات
    $categories = $pdo->query('SELECT * FROM categories ORDER BY name')->fetchAll();
}

// معاملات التقسيم والبحث
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10; // عدد المنتجات في الصفحة
$offset = ($page - 1) * $limit;
$search = trim($_GET['search'] ?? '');
$category_filter = trim($_GET['category'] ?? '');

try {
    // بناء شروط البحث
    $where_conditions = ['client_id = ?'];
    $params = [$client_id];

    if (!empty($search)) {
        $where_conditions[] = '(name LIKE ? OR description LIKE ?)';
        $search_term = '%' . $search . '%';
        $params[] = $search_term;
        $params[] = $search_term;
    }

    if (!empty($category_filter)) {
        $where_conditions[] = 'category = ?';
        $params[] = $category_filter;
    }

    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

    // عد إجمالي المنتجات
    $count_query = "SELECT COUNT(*) FROM cafeteria_items $where_clause";
    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute($params);
    $total_items = $count_stmt->fetchColumn();

    // حساب عدد الصفحات
    $total_pages = ceil($total_items / $limit);

    // جلب المنتجات للصفحة الحالية - مع التحقق من وجود العمود category
    $order_clause = "ORDER BY name";

    // التحقق من وجود عمود category
    $category_column_exists = false;
    try {
        $check_column = $pdo->query("SHOW COLUMNS FROM cafeteria_items LIKE 'category'");
        $category_column_exists = $check_column->rowCount() > 0;
    } catch (PDOException $e) {
        // في حالة الخطأ، نستخدم ترتيب بسيط
    }

    if ($category_column_exists) {
        $order_clause = "ORDER BY category, name";
    }

    $items_query = "SELECT * FROM cafeteria_items $where_clause $order_clause LIMIT $limit OFFSET $offset";
    $items_stmt = $pdo->prepare($items_query);
    $items_stmt->execute($params);
    $items = $items_stmt->fetchAll();

} catch (PDOException $e) {
    // إذا لم يكن عمود client_id موجود، جلب جميع المنتجات مع التقسيم
    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = '(name LIKE ? OR description LIKE ?)';
        $search_term = '%' . $search . '%';
        $params[] = $search_term;
        $params[] = $search_term;
    }

    if (!empty($category_filter)) {
        $where_conditions[] = 'category = ?';
        $params[] = $category_filter;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // عد إجمالي المنتجات
    $count_query = "SELECT COUNT(*) FROM cafeteria_items $where_clause";
    if (!empty($params)) {
        $count_stmt = $pdo->prepare($count_query);
        $count_stmt->execute($params);
    } else {
        $count_stmt = $pdo->query($count_query);
    }
    $total_items = $count_stmt->fetchColumn();

    // حساب عدد الصفحات
    $total_pages = ceil($total_items / $limit);

    // جلب المنتجات للصفحة الحالية - مع التحقق من وجود العمود category
    $order_clause = "ORDER BY name";

    // التحقق من وجود عمود category
    try {
        $check_column = $pdo->query("SHOW COLUMNS FROM cafeteria_items LIKE 'category'");
        if ($check_column->rowCount() > 0) {
            $order_clause = "ORDER BY category, name";
        }
    } catch (PDOException $e) {
        // في حالة الخطأ، نستخدم ترتيب بسيط
    }

    $items_query = "SELECT * FROM cafeteria_items $where_clause $order_clause LIMIT $limit OFFSET $offset";
    if (!empty($params)) {
        $items_stmt = $pdo->prepare($items_query);
        $items_stmt->execute($params);
    } else {
        $items_stmt = $pdo->query($items_query);
    }
    $items = $items_stmt->fetchAll();
}

// Get messages from session
if (isset($_SESSION['success'])) {
    $messages['success'] = $_SESSION['success'];
    unset($_SESSION['success']);
}

if (isset($_SESSION['error'])) {
    $messages['error'] = $_SESSION['error'];
    unset($_SESSION['error']);
}

// Now include the header and rest of the HTML
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($messages['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $messages['success']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($messages['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $messages['error']; ?>
            <?php if (strpos($messages['error'], 'قيود قاعدة البيانات') !== false): ?>
                <hr>
                <small>
                    <i class="fas fa-tools me-1"></i>
                    <a href="../simple_category_fix.php" class="text-white text-decoration-underline">
                        انقر هنا لتشغيل سكريبت الإصلاح التلقائي
                    </a>
                </small>
            <?php endif; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- إضافة منتج جديد -->
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>إضافة منتج جديد</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم المنتج</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="price" class="form-label">السعر</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="price" name="price" step="0.01" required>
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="category" class="form-label">التصنيف</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر التصنيف</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category['name']); ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        <button type="submit" name="add_item" class="btn btn-primary w-100">
                            <i class="fas fa-plus-circle me-2"></i>إضافة المنتج
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- عرض المنتجات -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-coffee me-2"></i>قائمة منتجات الكافتيريا</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchProducts" 
                                       placeholder="البحث في المنتجات...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterCategory">
                                <option value="">كل التصنيفات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category['name']); ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-tag me-2"></i>المنتج</th>
                                    <th><i class="fas fa-money-bill me-2"></i>السعر</th>
                                    <th><i class="fas fa-layer-group me-2"></i>التصنيف</th>
                                    <th><i class="fas fa-align-left me-2"></i>الوصف</th>
                                    <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($items)): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <i class="fas fa-coffee fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد منتجات متاحة</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                                        <td><?php echo number_format($item['price'], 2); ?> ج.م</td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo htmlspecialchars($item['category'] ?? 'غير محدد'); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($item['description'] ?? ''); ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-info me-1"
                                                    onclick="editItem(<?php echo htmlspecialchars(json_encode($item)); ?>)"
                                                    title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger"
                                                    onclick="deleteItem(<?php echo $item['id']; ?>)"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- معلومات التقسيم والتنقل -->
                    <?php if ($total_items > 0): ?>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="text-muted">
                            <small>
                                عرض <?php echo (($page - 1) * $limit) + 1; ?> إلى
                                <?php echo min($page * $limit, $total_items); ?> من
                                <?php echo $total_items; ?> منتج
                            </small>
                        </div>

                        <?php if ($total_pages > 1): ?>
                        <nav aria-label="تنقل الصفحات">
                            <ul class="pagination pagination-sm mb-0">
                                <!-- الصفحة السابقة -->
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($category_filter) ? '&category=' . urlencode($category_filter) : ''; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>

                                <!-- أرقام الصفحات -->
                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);

                                for ($i = $start_page; $i <= $end_page; $i++):
                                ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($category_filter) ? '&category=' . urlencode($category_filter) : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>

                                <!-- الصفحة التالية -->
                                <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($category_filter) ? '&category=' . urlencode($category_filter) : ''; ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- إضافة وإدارة التصنيفات -->
    <div class="col-md-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-tags me-2"></i>إدارة التصنيفات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <form method="post" class="needs-validation" novalidate>
                            <div class="input-group">
                                <input type="text" class="form-control" name="category_name" 
                                       placeholder="اسم التصنيف الجديد" required>
                                <button type="submit" name="add_category" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-2"></i>إضافة
                                </button>
                                <div class="invalid-feedback">يرجى إدخال اسم التصنيف</div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="searchCategories" 
                               placeholder="البحث في التصنيفات...">
                    </div>
                </div>
                <hr>
                <div class="row g-3" id="categoriesList">
                    <?php foreach ($categories as $category): ?>
                        <div class="col-md-3 category-item">
                            <div class="card">
                                <div class="card-body d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0"><?php echo htmlspecialchars($category['name']); ?></h6>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-info btn-sm" 
                                                onclick="editCategory(<?php echo htmlspecialchars(json_encode([
                                                    'category_id' => $category['category_id'],
                                                    'name' => $category['name']
                                                ])); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm delete-category" 
                                                data-category-id="<?php echo $category['category_id']; ?>"
                                                data-category-name="<?php echo htmlspecialchars($category['name']); ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل المنتج -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="item_id" id="edit_id">
                    <div class="mb-3">
                        <label class="form-label">اسم المنتج</label>
                        <input type="text" class="form-control" name="name" id="edit_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">السعر</label>
                        <input type="number" class="form-control" name="price" id="edit_price" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">التصنيف</label>
                        <select class="form-select" name="category" id="edit_category" required>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo htmlspecialchars($category['name']); ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" name="description" id="edit_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="edit_item" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل التصنيف -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل التصنيف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="category_id" id="edit_category_id">
                    <div class="mb-3">
                        <label class="form-label">اسم التصنيف</label>
                        <input type="text" class="form-control" name="category_name" 
                               id="edit_category_name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم التصنيف</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="edit_category" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editItem(item) {
    document.getElementById('edit_id').value = item.id;
    document.getElementById('edit_name').value = item.name;
    document.getElementById('edit_price').value = item.price;
    document.getElementById('edit_category').value = item.category;
    document.getElementById('edit_description').value = item.description;
    
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deleteItem(id) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        window.location.href = 'cafeteria.php?delete=' + id;
    }
}

// البحث في المنتجات مع إعادة التحميل
document.getElementById('searchProducts').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        const searchText = this.value.trim();
        const filterCategory = document.getElementById('filterCategory').value;

        // بناء URL مع معاملات البحث
        const params = new URLSearchParams();
        params.set('page', '1'); // العودة للصفحة الأولى عند البحث

        if (searchText) {
            params.set('search', searchText);
        }

        if (filterCategory) {
            params.set('category', filterCategory);
        }

        // إعادة تحميل الصفحة مع معاملات البحث
        window.location.href = 'cafeteria.php?' + params.toString();
    }, 500); // انتظار 500ms قبل البحث
});

// فلترة حسب التصنيف
document.getElementById('filterCategory').addEventListener('change', function() {
    const searchText = document.getElementById('searchProducts').value.trim();
    const filterCategory = this.value;

    // بناء URL مع معاملات البحث
    const params = new URLSearchParams();
    params.set('page', '1'); // العودة للصفحة الأولى عند الفلترة

    if (searchText) {
        params.set('search', searchText);
    }

    if (filterCategory) {
        params.set('category', filterCategory);
    }

    // إعادة تحميل الصفحة مع معاملات البحث
    window.location.href = 'cafeteria.php?' + params.toString();
});

// تعيين القيم الحالية للبحث والفلترة من URL
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const search = urlParams.get('search');
    const category = urlParams.get('category');

    if (search) {
        document.getElementById('searchProducts').value = search;
    }

    if (category) {
        document.getElementById('filterCategory').value = category;
    }
});

// البحث في التصنيفات
document.getElementById('searchCategories').addEventListener('input', function() {
    const searchText = this.value.toLowerCase();
    const categories = document.querySelectorAll('.category-item');
    
    categories.forEach(category => {
        const name = category.querySelector('.card-title').textContent.toLowerCase();
        category.style.display = name.includes(searchText) ? '' : 'none';
    });
});

// تحرير التصنيف
function editCategory(category) {
    document.getElementById('edit_category_id').value = category.category_id;
    document.getElementById('edit_category_name').value = category.name;
    new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
}

// حذف التصنيف
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.delete-category').forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.categoryId;
            const categoryName = this.dataset.categoryName;
            
            Swal.fire({
                title: 'تأكيد حذف التصنيف',
                html: `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                        <p>هل أنت متأكد من حذف التصنيف <strong>"${categoryName}"</strong>؟</p>
                        <div class="alert alert-warning mt-3">
                            <small><i class="fas fa-info-circle me-1"></i>
                            تأكد من عدم وجود منتجات مرتبطة بهذا التصنيف قبل الحذف
                            </small>
                        </div>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-trash me-1"></i>نعم، احذف',
                cancelButtonText: '<i class="fas fa-times me-1"></i>إلغاء',
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                reverseButtons: true,
                customClass: {
                    confirmButton: 'btn btn-danger',
                    cancelButton: 'btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // عرض رسالة تحميل
                    Swal.fire({
                        title: 'جاري الحذف...',
                        text: 'يرجى الانتظار',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // إرسال طلب الحذف
                    window.location.href = `cafeteria.php?delete_category=${categoryId}`;
                }
            });
        });
    });
});

// التحقق من صحة النماذج
document.querySelectorAll('.needs-validation').forEach(form => {
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>

