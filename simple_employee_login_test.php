<?php
/**
 * اختبار مبسط لتسجيل دخول الموظف
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/database.php';

$error = '';
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            // البحث عن الموظف
            $stmt = $pdo->prepare("
                SELECT e.*, c.business_name, c.owner_name, c.is_active as client_active
                FROM employees e
                JOIN clients c ON e.client_id = c.client_id
                WHERE e.username = ?
            ");
            $stmt->execute([$username]);
            $employee = $stmt->fetch();
            
            if (!$employee) {
                $error = 'اسم المستخدم غير موجود';
            } elseif (!$employee['is_active']) {
                $error = 'حساب الموظف غير مفعل. يرجى التواصل مع الإدارة';
            } elseif (!$employee['client_active']) {
                $error = 'حساب المحل غير مفعل حالياً. يرجى التواصل مع الإدارة';
            } elseif (!password_verify($password, $employee['password_hash'])) {
                $error = 'كلمة المرور غير صحيحة';
            } else {
                // تسجيل دخول ناجح
                $_SESSION['employee_id'] = $employee['id'];
                $_SESSION['employee_name'] = $employee['name'];
                $_SESSION['employee_role'] = $employee['role'];
                $_SESSION['client_id'] = $employee['client_id'];
                $_SESSION['business_name'] = $employee['business_name'];
                $_SESSION['owner_name'] = $employee['owner_name'];
                
                // تحديث آخر تسجيل دخول
                $updateStmt = $pdo->prepare("UPDATE employees SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
                $updateStmt->execute([$employee['id']]);
                
                $success = 'تم تسجيل الدخول بنجاح! مرحباً ' . $employee['name'];
            }
            
        } catch (PDOException $e) {
            $error = 'حدث خطأ في النظام: ' . $e->getMessage();
        }
    }
}

// عرض معلومات الجلسة الحالية
$current_session = '';
if (isset($_SESSION['employee_id'])) {
    $current_session = "مسجل دخول كموظف: " . $_SESSION['employee_name'] . " (" . $_SESSION['employee_role'] . ")";
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل دخول الموظف - مبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-container { max-width: 400px; margin: 50px auto; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .card-header { background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 15px 15px 0 0 !important; }
        .form-control { border-radius: 10px; border: 2px solid #e9ecef; }
        .form-control:focus { border-color: #667eea; box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25); }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 10px; }
        .session-info { background: #f8f9fa; border-radius: 10px; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="card">
                <div class="card-header text-white text-center">
                    <h4><i class="fas fa-user-tie me-2"></i>تسجيل دخول الموظف - اختبار</h4>
                </div>
                <div class="card-body p-4">
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($current_session): ?>
                        <div class="session-info">
                            <h6><i class="fas fa-info-circle me-2"></i>الجلسة الحالية:</h6>
                            <p class="mb-0"><?php echo htmlspecialchars($current_session); ?></p>
                            <a href="?logout=1" class="btn btn-sm btn-outline-danger mt-2">
                                <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>اسم المستخدم
                            </label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </button>
                    </form>
                    
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            <a href="test_employee_login_debug.php" class="text-decoration-none">
                                <i class="fas fa-bug me-1"></i>أدوات التشخيص المتقدمة
                            </a>
                        </small>
                    </div>
                    
                </div>
            </div>
            
            <!-- معلومات إضافية للمطور -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات للمطور</h6>
                </div>
                <div class="card-body">
                    <small>
                        <strong>الملفات المستخدمة:</strong><br>
                        • config/database.php<br>
                        • جدول employees<br>
                        • جدول clients<br><br>
                        
                        <strong>الاستعلام المستخدم:</strong><br>
                        <code>SELECT e.*, c.business_name, c.owner_name, c.is_active as client_active FROM employees e JOIN clients c ON e.client_id = c.client_id WHERE e.username = ?</code>
                    </small>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php
// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}
?>
