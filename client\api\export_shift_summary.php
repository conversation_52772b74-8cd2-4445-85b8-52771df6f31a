<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: ../login.php');
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من وجود معرف الملخص
if (!isset($_GET['summary_id']) || !is_numeric($_GET['summary_id'])) {
    die('معرف الملخص مطلوب');
}

$summary_id = (int)$_GET['summary_id'];

try {
    // جلب تفاصيل الملخص
    $summary_query = "
        SELECT s.*, ss.*,
               supervisor.name as supervisor_name,
               generator.name as generated_by_name,
               c.business_name as client_name
        FROM shift_summaries ss
        JOIN shifts s ON ss.shift_id = s.shift_id
        JOIN clients c ON s.client_id = c.client_id
        LEFT JOIN employees supervisor ON s.shift_supervisor = supervisor.id
        LEFT JOIN employees generator ON ss.generated_by = generator.id
        WHERE ss.summary_id = ? AND s.client_id = ?
    ";

    $stmt = $pdo->prepare($summary_query);
    $stmt->execute([$summary_id, $client_id]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$summary) {
        die('الملخص غير موجود');
    }

    // جلب تفاصيل الموظفين
    $employees_query = "
        SELECT e.name, e.role, sa.check_in_time, sa.check_out_time, 
               sa.actual_hours, sa.overtime_hours, sa.status,
               sa.late_minutes, sa.early_leave_minutes
        FROM employee_shifts es
        JOIN employees e ON es.employee_id = e.id
        LEFT JOIN shift_attendance sa ON es.assignment_id = sa.assignment_id
        WHERE es.shift_id = ?
        ORDER BY e.name
    ";

    $stmt = $pdo->prepare($employees_query);
    $stmt->execute([$summary['shift_id']]);
    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الأحداث المهمة
    $events_query = "
        SELECT event_type, event_title, event_description, severity, 
               recorded_at, is_resolved, e.name as employee_name
        FROM shift_events se
        LEFT JOIN employees e ON se.employee_id = e.id
        WHERE se.shift_id = ?
        ORDER BY se.recorded_at DESC
    ";

    $stmt = $pdo->prepare($events_query);
    $stmt->execute([$summary['shift_id']]);
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تحديد نوع التصدير
    $export_type = $_GET['type'] ?? 'html';

    if ($export_type === 'csv') {
        // تصدير CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="shift_summary_' . $summary['shift_date'] . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // معلومات الشيفت
        fputcsv($output, ['ملخص الشيفت - ' . $summary['client_name']]);
        fputcsv($output, ['اسم الشيفت', $summary['shift_name']]);
        fputcsv($output, ['التاريخ', $summary['shift_date']]);
        fputcsv($output, ['الوقت', $summary['start_time'] . ' - ' . $summary['end_time']]);
        fputcsv($output, ['المشرف', $summary['supervisor_name'] ?? 'غير محدد']);
        fputcsv($output, []);
        
        // إحصائيات
        fputcsv($output, ['الإحصائيات']);
        fputcsv($output, ['الموظفين المخصصين', $summary['total_employees_assigned']]);
        fputcsv($output, ['الموظفين الحاضرين', $summary['total_employees_attended']]);
        fputcsv($output, ['نسبة الحضور', $summary['attendance_percentage'] . '%']);
        fputcsv($output, ['ساعات العمل', $summary['total_work_hours']]);
        fputcsv($output, ['ساعات إضافية', $summary['total_overtime_hours']]);
        fputcsv($output, ['نقاط الأداء', $summary['performance_score'] . '/10']);
        fputcsv($output, ['إجمالي الأحداث', $summary['total_events']]);
        fputcsv($output, ['أحداث حرجة', $summary['critical_events']]);
        fputcsv($output, ['مشاكل غير محلولة', $summary['unresolved_issues']]);
        fputcsv($output, []);
        
        // تفاصيل الموظفين
        fputcsv($output, ['تفاصيل الموظفين']);
        fputcsv($output, ['الاسم', 'الدور', 'وقت الحضور', 'وقت الانصراف', 'ساعات العمل', 'ساعات إضافية', 'التأخير (دقيقة)', 'الحالة']);
        
        foreach ($employees as $emp) {
            fputcsv($output, [
                $emp['name'],
                $emp['role'],
                $emp['check_in_time'] ? date('H:i:s', strtotime($emp['check_in_time'])) : 'لم يحضر',
                $emp['check_out_time'] ? date('H:i:s', strtotime($emp['check_out_time'])) : 'لم ينصرف',
                $emp['actual_hours'] ?? 0,
                $emp['overtime_hours'] ?? 0,
                $emp['late_minutes'] ?? 0,
                $emp['status'] ?? 'غير محدد'
            ]);
        }
        
        fputcsv($output, []);
        
        // الأحداث
        if (!empty($events)) {
            fputcsv($output, ['الأحداث']);
            fputcsv($output, ['الوقت', 'النوع', 'العنوان', 'الأهمية', 'الموظف', 'محلول']);
            
            foreach ($events as $event) {
                fputcsv($output, [
                    date('H:i:s', strtotime($event['recorded_at'])),
                    $event['event_type'],
                    $event['event_title'],
                    $event['severity'],
                    $event['employee_name'] ?? 'غير محدد',
                    $event['is_resolved'] ? 'نعم' : 'لا'
                ]);
            }
        }
        
        fclose($output);
        exit;
    }

    // تصدير HTML (افتراضي)
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ملخص الشيفت - <?php echo htmlspecialchars($summary['shift_name']); ?></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            @media print {
                .no-print { display: none !important; }
                .page-break { page-break-before: always; }
            }
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .header-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
            .stat-card { border-left: 4px solid #007bff; }
            .performance-excellent { color: #28a745; }
            .performance-good { color: #ffc107; }
            .performance-poor { color: #dc3545; }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <!-- رأس التقرير -->
            <div class="header-section p-4 mb-4 rounded">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-chart-pie me-2"></i>
                            ملخص الشيفت
                        </h1>
                        <h3><?php echo htmlspecialchars($summary['shift_name']); ?></h3>
                        <p class="mb-0"><?php echo htmlspecialchars($summary['client_name']); ?></p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="no-print">
                            <button onclick="window.print()" class="btn btn-light me-2">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <a href="?summary_id=<?php echo $summary_id; ?>&type=csv" class="btn btn-success">
                                <i class="fas fa-download"></i> تصدير CSV
                            </a>
                        </div>
                        <div class="mt-3">
                            <small>تاريخ الإنشاء: <?php echo date('Y-m-d H:i'); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات أساسية -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card stat-card">
                        <div class="card-body">
                            <h5 class="card-title">معلومات الشيفت</h5>
                            <table class="table table-sm">
                                <tr><td><strong>التاريخ:</strong></td><td><?php echo $summary['shift_date']; ?></td></tr>
                                <tr><td><strong>الوقت:</strong></td><td><?php echo $summary['start_time']; ?> - <?php echo $summary['end_time']; ?></td></tr>
                                <tr><td><strong>المشرف:</strong></td><td><?php echo htmlspecialchars($summary['supervisor_name'] ?? 'غير محدد'); ?></td></tr>
                                <tr><td><strong>الحالة:</strong></td><td>
                                    <span class="badge bg-<?php echo $summary['status'] == 'completed' ? 'success' : 'warning'; ?>">
                                        <?php echo $summary['status'] == 'completed' ? 'مكتمل' : $summary['status']; ?>
                                    </span>
                                </td></tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card stat-card">
                        <div class="card-body">
                            <h5 class="card-title">نقاط الأداء</h5>
                            <div class="text-center">
                                <h2 class="<?php 
                                    echo $summary['performance_score'] >= 8 ? 'performance-excellent' : 
                                        ($summary['performance_score'] >= 6 ? 'performance-good' : 'performance-poor'); 
                                ?>">
                                    <?php echo number_format($summary['performance_score'], 1); ?>/10
                                </h2>
                                <p class="mb-0">
                                    <?php 
                                    echo $summary['performance_score'] >= 8 ? 'ممتاز' : 
                                        ($summary['performance_score'] >= 6 ? 'جيد' : 
                                        ($summary['performance_score'] >= 4 ? 'مقبول' : 'ضعيف')); 
                                    ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الحضور -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $summary['total_employees_assigned']; ?></h3>
                            <p class="mb-0">موظفين مخصصين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo $summary['total_employees_attended']; ?></h3>
                            <p class="mb-0">موظفين حاضرين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?php echo number_format($summary['attendance_percentage'], 1); ?>%</h3>
                            <p class="mb-0">نسبة الحضور</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo number_format($summary['total_work_hours'], 1); ?></h3>
                            <p class="mb-0">ساعات العمل</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الموظفين -->
            <?php if (!empty($employees)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل الموظفين</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الدور</th>
                                    <th>وقت الحضور</th>
                                    <th>وقت الانصراف</th>
                                    <th>ساعات العمل</th>
                                    <th>ساعات إضافية</th>
                                    <th>التأخير</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $emp): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($emp['name']); ?></strong></td>
                                    <td><?php echo $emp['role']; ?></td>
                                    <td><?php echo $emp['check_in_time'] ? date('H:i:s', strtotime($emp['check_in_time'])) : '<span class="text-muted">لم يحضر</span>'; ?></td>
                                    <td><?php echo $emp['check_out_time'] ? date('H:i:s', strtotime($emp['check_out_time'])) : '<span class="text-muted">لم ينصرف</span>'; ?></td>
                                    <td><?php echo number_format($emp['actual_hours'] ?? 0, 2); ?></td>
                                    <td><?php echo number_format($emp['overtime_hours'] ?? 0, 2); ?></td>
                                    <td>
                                        <?php if ($emp['late_minutes'] > 0): ?>
                                            <span class="text-danger"><?php echo $emp['late_minutes']; ?> دقيقة</span>
                                        <?php else: ?>
                                            <span class="text-success">في الوقت</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $emp['status'] == 'present' ? 'success' : 'secondary'; ?>">
                                            <?php echo $emp['status'] ?? 'غير محدد'; ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- الأحداث المهمة -->
            <?php if (!empty($events)): ?>
            <div class="card mb-4 page-break">
                <div class="card-header">
                    <h5 class="mb-0">سجل الأحداث</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>النوع</th>
                                    <th>العنوان</th>
                                    <th>الأهمية</th>
                                    <th>الموظف</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($events as $event): ?>
                                <tr>
                                    <td><?php echo date('H:i:s', strtotime($event['recorded_at'])); ?></td>
                                    <td><?php echo $event['event_type']; ?></td>
                                    <td><?php echo htmlspecialchars($event['event_title']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $event['severity'] == 'critical' ? 'danger' : 
                                                ($event['severity'] == 'high' ? 'warning' : 'info'); 
                                        ?>">
                                            <?php echo $event['severity']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($event['employee_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $event['is_resolved'] ? 'success' : 'warning'; ?>">
                                            <?php echo $event['is_resolved'] ? 'محلول' : 'غير محلول'; ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- ملاحظات الشيفت -->
            <?php if ($summary['shift_notes']): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">ملاحظات الشيفت</h5>
                </div>
                <div class="card-body">
                    <p><?php echo nl2br(htmlspecialchars($summary['shift_notes'])); ?></p>
                </div>
            </div>
            <?php endif; ?>

            <!-- تذييل التقرير -->
            <div class="text-center mt-4 pt-4 border-top">
                <small class="text-muted">
                    تم إنشاء هذا التقرير في: <?php echo date('Y-m-d H:i:s'); ?><br>
                    <?php if ($summary['generated_by_name']): ?>
                        بواسطة: <?php echo htmlspecialchars($summary['generated_by_name']); ?><br>
                    <?php endif; ?>
                    نظام إدارة الشيفتات - <?php echo htmlspecialchars($summary['client_name']); ?>
                </small>
            </div>
        </div>
    </body>
    </html>
    <?php

} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}
?>
