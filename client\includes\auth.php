<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit();
}

// تضمين قاعدة البيانات للتحقق من الصلاحيات
if (!isset($pdo)) {
    require_once __DIR__ . '/../../config/database.php';
}

/**
 * التحقق من صلاحية الوصول لصفحة معينة
 * @param string $page_name اسم الصفحة
 * @return bool هل العميل لديه صلاحية الوصول
 */
function hasPagePermission($page_name) {
    global $pdo;

    // إذا لم يكن هناك عميل أو موظف مسجل دخول
    if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
        return false;
    }

    // إذا كان موظف، التحقق من صلاحياته
    if (isset($_SESSION['employee_id'])) {
        // تضمين ملف صلاحيات الموظفين إذا لم يكن محملاً
        if (!function_exists('employeeHasPermission')) {
            require_once __DIR__ . '/employee-auth.php';
        }

        // تحويل أسماء الصفحات إلى صلاحيات الموظفين
        $page_to_permission = [
            'dashboard' => 'dashboard_access',
            'sessions' => 'sessions_access',
            'customers' => 'customers_access',
            'devices' => 'devices_access',
            'cafeteria' => 'cafeteria_access',
            'orders' => 'orders_access',
            'employees' => 'manage_employees',
            'reports' => 'reports_access'
        ];

        $permission = $page_to_permission[$page_name] ?? $page_name . '_access';
        return employeeHasPermission($permission);
    }

    // إذا كان عميل، استخدام النظام القديم
    try {
        // التحقق من وجود جدول صلاحيات الصفحات
        $table_check = $pdo->query("SHOW TABLES LIKE 'client_pages'");
        if ($table_check->rowCount() == 0) {
            // إذا لم يكن النظام مفعل، السماح بالوصول لجميع الصفحات
            return true;
        }

        $stmt = $pdo->prepare("
            SELECT
                COALESCE(cpp.is_enabled, cp.is_default) as has_permission
            FROM client_pages cp
            LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
            WHERE cp.page_name = ? AND cp.is_active = TRUE
        ");
        $stmt->execute([$_SESSION['client_id'], $page_name]);
        $result = $stmt->fetch();

        return $result ? (bool)$result['has_permission'] : false;

    } catch (Exception $e) {
        // في حالة حدوث خطأ، السماح بالوصول (للأمان)
        return true;
    }
}

/**
 * التحقق من صلاحية الصفحة الحالية وإعادة التوجيه إذا لم تكن مسموحة
 */
function checkCurrentPagePermission() {
    $current_page = basename($_SERVER['PHP_SELF'], '.php');

    // الصفحات المستثناة من فحص الصلاحيات
    $excluded_pages = ['login', 'logout', 'profile', 'employee-login', 'test_employee_redirect_fix'];

    if (!in_array($current_page, $excluded_pages)) {
        if (!hasPagePermission($current_page)) {
            // إعادة التوجيه إلى لوحة التحكم مع رسالة خطأ
            $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
            header('Location: dashboard.php');
            exit();
        }
    }
}

/**
 * جلب قائمة الصفحات المسموحة للعميل الحالي
 * @return array قائمة الصفحات المسموحة
 */
function getAllowedPages() {
    global $pdo;

    if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
        return [];
    }

    // إذا كان موظف، إرجاع الصفحات المسموحة له
    if (isset($_SESSION['employee_id'])) {
        // تضمين ملف صلاحيات الموظفين إذا لم يكن محملاً
        if (!function_exists('employeeHasPermission')) {
            require_once __DIR__ . '/employee-auth.php';
        }

        $allowed_pages = [];
        $all_pages = [
            'dashboard' => ['label' => 'لوحة التحكم', 'icon' => 'fas fa-tachometer-alt', 'url' => 'dashboard.php'],
            'sessions' => ['label' => 'الجلسات', 'icon' => 'fas fa-clock', 'url' => 'sessions.php'],
            'customers' => ['label' => 'العملاء', 'icon' => 'fas fa-users', 'url' => 'customers.php'],
            'devices' => ['label' => 'الأجهزة', 'icon' => 'fas fa-desktop', 'url' => 'devices.php'],
            'cafeteria' => ['label' => 'الكافتيريا', 'icon' => 'fas fa-coffee', 'url' => 'cafeteria.php'],
            'orders' => ['label' => 'الطلبات', 'icon' => 'fas fa-shopping-cart', 'url' => 'orders.php'],
            'employees' => ['label' => 'الموظفين', 'icon' => 'fas fa-user-tie', 'url' => 'employees.php'],
            'reports' => ['label' => 'التقارير', 'icon' => 'fas fa-chart-bar', 'url' => 'reports.php']
        ];

        foreach ($all_pages as $page_name => $page_info) {
            if (hasPagePermission($page_name)) {
                $allowed_pages[] = array_merge(['page_name' => $page_name], $page_info);
            }
        }

        return $allowed_pages;
    }

    // إذا كان عميل، استخدام النظام القديم
    try {
        // التحقق من وجود جدول صلاحيات الصفحات
        $table_check = $pdo->query("SHOW TABLES LIKE 'client_pages'");
        if ($table_check->rowCount() == 0) {
            // إذا لم يكن النظام مفعل، إرجاع جميع الصفحات الأساسية
            return [
                'dashboard', 'devices', 'sessions', 'customers', 'reports',
                'invoices', 'settings', 'profile'
            ];
        }

        $stmt = $pdo->prepare("
            SELECT cp.page_name, cp.page_label, cp.page_url, cp.page_icon, cp.category
            FROM client_pages cp
            LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
            WHERE cp.is_active = TRUE
            AND COALESCE(cpp.is_enabled, cp.is_default) = TRUE
            ORDER BY cp.category, cp.page_label
        ");
        $stmt->execute([$_SESSION['client_id']]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        // في حالة حدوث خطأ، إرجاع الصفحات الأساسية
        return [
            'dashboard', 'devices', 'sessions', 'customers', 'reports',
            'invoices', 'settings', 'profile'
        ];
    }
}

// فحص صلاحية الصفحة الحالية
checkCurrentPagePermission();