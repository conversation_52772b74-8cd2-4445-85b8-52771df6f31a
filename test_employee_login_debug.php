<?php
/**
 * اختبار وتشخيص مشكلة تسجيل دخول الموظف
 * هذا الملف يساعد في تحديد سبب عدم عمل تسجيل دخول الموظف
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشكلة تسجيل دخول الموظف</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        .test-card { margin: 20px 0; }
        .test-success { color: #28a745; }
        .test-error { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-info { color: #17a2b8; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
        .debug-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <div class='row justify-content-center'>
        <div class='col-md-12'>
            <div class='card test-card'>
                <div class='card-header bg-primary text-white'>
                    <h4><i class='fas fa-bug me-2'></i>تشخيص مشكلة تسجيل دخول الموظف</h4>
                    <p class='mb-0'>فحص شامل لنظام تسجيل دخول الموظفين</p>
                </div>
                <div class='card-body'>";

// 1. فحص قاعدة البيانات والاتصال
echo "<div class='debug-section'>";
echo "<h5><i class='fas fa-database me-2'></i>1. فحص قاعدة البيانات</h5>";

try {
    $pdo->query("SELECT 1");
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "✓ الاتصال بقاعدة البيانات يعمل بشكل صحيح";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i>";
    echo "✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    echo "</div>";
}

// فحص وجود جدول الموظفين
try {
    $result = $pdo->query("SHOW TABLES LIKE 'employees'");
    if ($result->rowCount() > 0) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "✓ جدول الموظفين موجود";
        echo "</div>";
        
        // فحص بنية الجدول
        $columns = $pdo->query("DESCRIBE employees")->fetchAll();
        echo "<h6>بنية جدول الموظفين:</h6>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead><tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-times-circle me-2'></i>";
        echo "✗ جدول الموظفين غير موجود!";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i>";
    echo "✗ خطأ في فحص جدول الموظفين: " . $e->getMessage();
    echo "</div>";
}

echo "</div>";

// 2. فحص بيانات الموظفين
echo "<div class='debug-section'>";
echo "<h5><i class='fas fa-users me-2'></i>2. فحص بيانات الموظفين</h5>";

try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM employees");
    $total = $stmt->fetch()['total'];
    
    echo "<div class='alert alert-info'>";
    echo "<i class='fas fa-info-circle me-2'></i>";
    echo "إجمالي عدد الموظفين: " . $total;
    echo "</div>";
    
    if ($total > 0) {
        $stmt = $pdo->query("
            SELECT e.*, c.business_name, c.is_active as client_active 
            FROM employees e 
            LEFT JOIN clients c ON e.client_id = c.client_id 
            LIMIT 5
        ");
        $employees = $stmt->fetchAll();
        
        echo "<h6>عينة من الموظفين:</h6>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead><tr><th>ID</th><th>الاسم</th><th>اسم المستخدم</th><th>الدور</th><th>نشط</th><th>العميل نشط</th><th>اسم المحل</th></tr></thead>";
        echo "<tbody>";
        foreach ($employees as $emp) {
            $activeClass = $emp['is_active'] ? 'text-success' : 'text-danger';
            $clientActiveClass = $emp['client_active'] ? 'text-success' : 'text-danger';
            echo "<tr>";
            echo "<td>" . $emp['id'] . "</td>";
            echo "<td>" . $emp['name'] . "</td>";
            echo "<td>" . $emp['username'] . "</td>";
            echo "<td>" . $emp['role'] . "</td>";
            echo "<td class='{$activeClass}'>" . ($emp['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "<td class='{$clientActiveClass}'>" . ($emp['client_active'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . ($emp['business_name'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i>";
    echo "✗ خطأ في فحص بيانات الموظفين: " . $e->getMessage();
    echo "</div>";
}

echo "</div>";

// 3. فحص الملفات المطلوبة
echo "<div class='debug-section'>";
echo "<h5><i class='fas fa-file-code me-2'></i>3. فحص الملفات المطلوبة</h5>";

$required_files = [
    'client/employee-login.php' => 'صفحة تسجيل دخول الموظف',
    'client/includes/auth.php' => 'ملف التحقق من الصلاحيات',
    'client/includes/employee-auth.php' => 'ملف صلاحيات الموظفين',
    'config/database.php' => 'ملف قاعدة البيانات'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "✓ {$description}: موجود";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-times-circle me-2'></i>";
        echo "✗ {$description}: غير موجود ({$file})";
        echo "</div>";
    }
}

echo "</div>";

// 4. اختبار تسجيل دخول تجريبي
echo "<div class='debug-section'>";
echo "<h5><i class='fas fa-vial me-2'></i>4. اختبار تسجيل دخول تجريبي</h5>";

if (isset($_POST['test_login'])) {
    $test_username = $_POST['test_username'];
    $test_password = $_POST['test_password'];
    
    echo "<div class='alert alert-info'>";
    echo "<i class='fas fa-info-circle me-2'></i>";
    echo "محاولة تسجيل دخول باسم المستخدم: " . htmlspecialchars($test_username);
    echo "</div>";
    
    try {
        $stmt = $pdo->prepare("
            SELECT e.*, c.business_name, c.owner_name, c.is_active as client_active
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.username = ?
        ");
        $stmt->execute([$test_username]);
        $employee = $stmt->fetch();
        
        if ($employee) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-check-circle me-2'></i>";
            echo "✓ تم العثور على الموظف: " . $employee['name'];
            echo "</div>";
            
            // فحص حالة الموظف
            if (!$employee['is_active']) {
                echo "<div class='alert alert-warning'>";
                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                echo "⚠ حساب الموظف غير مفعل";
                echo "</div>";
            }
            
            // فحص حالة العميل
            if (!$employee['client_active']) {
                echo "<div class='alert alert-warning'>";
                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                echo "⚠ حساب المحل غير مفعل";
                echo "</div>";
            }
            
            // فحص كلمة المرور
            if (password_verify($test_password, $employee['password_hash'])) {
                echo "<div class='alert alert-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "✓ كلمة المرور صحيحة";
                echo "</div>";
                
                if ($employee['is_active'] && $employee['client_active']) {
                    echo "<div class='alert alert-success'>";
                    echo "<i class='fas fa-thumbs-up me-2'></i>";
                    echo "<strong>النتيجة: يجب أن يعمل تسجيل الدخول بشكل صحيح!</strong>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-warning'>";
                    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                    echo "<strong>النتيجة: تسجيل الدخول سيفشل بسبب عدم تفعيل الحساب</strong>";
                    echo "</div>";
                }
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "✗ كلمة المرور غير صحيحة";
                echo "</div>";
            }
            
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "✗ لم يتم العثور على موظف بهذا الاسم";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-times-circle me-2'></i>";
        echo "✗ خطأ في اختبار تسجيل الدخول: " . $e->getMessage();
        echo "</div>";
    }
}

// نموذج اختبار تسجيل الدخول
echo "<form method='POST' class='mt-3'>";
echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<input type='text' name='test_username' class='form-control' placeholder='اسم المستخدم' required>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<input type='password' name='test_password' class='form-control' placeholder='كلمة المرور' required>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<button type='submit' name='test_login' class='btn btn-primary'>";
echo "<i class='fas fa-test-tube me-2'></i>اختبار تسجيل الدخول";
echo "</button>";
echo "</div>";
echo "</div>";
echo "</form>";

echo "</div>";

// 5. فحص الجلسات
echo "<div class='debug-section'>";
echo "<h5><i class='fas fa-user-clock me-2'></i>5. فحص الجلسات الحالية</h5>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<div class='alert alert-info'>";
echo "<i class='fas fa-info-circle me-2'></i>";
echo "حالة الجلسة: " . (session_status() === PHP_SESSION_ACTIVE ? 'نشطة' : 'غير نشطة');
echo "</div>";

if (!empty($_SESSION)) {
    echo "<h6>محتويات الجلسة الحالية:</h6>";
    echo "<div class='code-block'>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "لا توجد بيانات في الجلسة الحالية";
    echo "</div>";
}

echo "</div>";

// 6. روابط للاختبار
echo "<div class='debug-section'>";
echo "<h5><i class='fas fa-external-link-alt me-2'></i>6. روابط للاختبار</h5>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<a href='client/employee-login.php' class='btn btn-outline-primary btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-sign-in-alt me-1'></i>صفحة تسجيل دخول الموظف";
echo "</a>";
echo "<a href='client/dashboard.php' class='btn btn-outline-info btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-tachometer-alt me-1'></i>لوحة التحكم";
echo "</a>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<a href='test_auth_fix.php' class='btn btn-outline-success btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-shield-alt me-1'></i>اختبار ملف المصادقة";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "            </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
