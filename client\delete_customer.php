<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من معرف العميل المراد حذفه
$customer_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($customer_id <= 0) {
    $_SESSION['error'] = 'معرف العميل غير صحيح';
    header('Location: customers.php');
    exit;
}

try {
    // التحقق من وجود العميل وملكيته للعميل الحالي
    $check_stmt = $pdo->prepare("SELECT customer_id, name FROM customers WHERE customer_id = ? AND client_id = ?");
    $check_stmt->execute([$customer_id, $client_id]);
    $customer = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$customer) {
        $_SESSION['error'] = 'العميل غير موجود أو غير مصرح بحذفه';
        header('Location: customers.php');
        exit;
    }
    
    // التحقق من وجود جلسات مرتبطة بالعميل
    $sessions_check = $pdo->prepare("SELECT COUNT(*) FROM sessions WHERE customer_id = ?");
    $sessions_check->execute([$customer_id]);
    $sessions_count = $sessions_check->fetchColumn();
    
    if ($sessions_count > 0) {
        // إذا كان هناك جلسات، نقوم بإلغاء ربطها بدلاً من حذف العميل
        $update_sessions = $pdo->prepare("UPDATE sessions SET customer_id = NULL WHERE customer_id = ?");
        $update_sessions->execute([$customer_id]);
        
        $_SESSION['warning'] = "تم إلغاء ربط العميل من $sessions_count جلسة قبل الحذف";
    }
    
    // حذف العميل
    $delete_stmt = $pdo->prepare("DELETE FROM customers WHERE customer_id = ? AND client_id = ?");
    $result = $delete_stmt->execute([$customer_id, $client_id]);
    
    if ($result && $delete_stmt->rowCount() > 0) {
        $_SESSION['success'] = "تم حذف العميل '{$customer['name']}' بنجاح";
    } else {
        $_SESSION['error'] = 'فشل في حذف العميل';
    }
    
} catch (PDOException $e) {
    error_log('خطأ في حذف العميل: ' . $e->getMessage());
    $_SESSION['error'] = 'حدث خطأ أثناء حذف العميل';
}

header('Location: customers.php');
exit;
?>
