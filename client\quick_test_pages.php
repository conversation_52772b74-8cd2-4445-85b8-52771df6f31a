<?php
/**
 * اختبار سريع لنظام الصفحات
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار سريع - نظام الصفحات</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='row'>
        <div class='col-md-8 mx-auto'>
            <div class='card shadow'>
                <div class='card-header bg-primary text-white text-center'>
                    <h4><i class='fas fa-rocket me-2'></i>اختبار سريع - نظام الصفحات</h4>
                </div>
                <div class='card-body'>";

try {
    // فحص الجداول الأساسية
    $tables_check = [
        'employees' => 'الموظفين',
        'pages' => 'الصفحات', 
        'employee_pages' => 'صفحات الموظفين'
    ];
    
    $all_good = true;
    
    echo "<h5><i class='fas fa-database me-2'></i>فحص الجداول</h5>";
    
    foreach ($tables_check as $table => $name) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p class='text-success'><i class='fas fa-check me-2'></i>جدول $name: $count سجل</p>";
        } catch (Exception $e) {
            echo "<p class='text-danger'><i class='fas fa-times me-2'></i>جدول $name: غير موجود</p>";
            $all_good = false;
        }
    }
    
    if (!$all_good) {
        echo "<div class='alert alert-warning'>
                <p><strong>تحذير:</strong> بعض الجداول غير موجودة.</p>
                <a href='setup_permissions_quick.php' class='btn btn-warning'>إعداد النظام</a>
              </div>";
    } else {
        // عرض إحصائيات سريعة
        echo "<hr>";
        echo "<h5><i class='fas fa-chart-pie me-2'></i>إحصائيات سريعة</h5>";
        
        // عدد الصفحات المتاحة
        $stmt = $pdo->query("SELECT COUNT(*) FROM pages WHERE is_active = 1");
        $pages_count = $stmt->fetchColumn();
        
        // عدد الموظفين مع صلاحيات مخصصة
        $stmt = $pdo->query("SELECT COUNT(*) FROM employees WHERE custom_permissions = 1");
        $custom_employees = $stmt->fetchColumn();
        
        // عدد الموظفين مع صفحات مخصصة
        $stmt = $pdo->query("SELECT COUNT(DISTINCT employee_id) FROM employee_pages");
        $employees_with_pages = $stmt->fetchColumn();
        
        echo "<div class='row text-center'>
                <div class='col-md-4'>
                    <div class='card border-info'>
                        <div class='card-body'>
                            <h3 class='text-info'>$pages_count</h3>
                            <p>صفحة متاحة</p>
                        </div>
                    </div>
                </div>
                <div class='col-md-4'>
                    <div class='card border-warning'>
                        <div class='card-body'>
                            <h3 class='text-warning'>$custom_employees</h3>
                            <p>موظف بصلاحيات مخصصة</p>
                        </div>
                    </div>
                </div>
                <div class='col-md-4'>
                    <div class='card border-success'>
                        <div class='card-body'>
                            <h3 class='text-success'>$employees_with_pages</h3>
                            <p>موظف بصفحات مخصصة</p>
                        </div>
                    </div>
                </div>
              </div>";
        
        // عرض الصفحات حسب الفئة
        echo "<hr>";
        echo "<h5><i class='fas fa-sitemap me-2'></i>الصفحات حسب الفئة</h5>";
        
        $stmt = $pdo->query("
            SELECT category, COUNT(*) as count, 
                   GROUP_CONCAT(page_label ORDER BY page_label SEPARATOR '، ') as pages
            FROM pages 
            WHERE is_active = 1 
            GROUP BY category 
            ORDER BY category
        ");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($categories) > 0) {
            echo "<div class='accordion' id='categoriesAccordion'>";
            $index = 0;
            foreach ($categories as $cat) {
                $index++;
                echo "<div class='accordion-item'>
                        <h2 class='accordion-header' id='heading$index'>
                            <button class='accordion-button collapsed' type='button' 
                                    data-bs-toggle='collapse' data-bs-target='#collapse$index'>
                                <strong>{$cat['category']}</strong> 
                                <span class='badge bg-primary ms-2'>{$cat['count']}</span>
                            </button>
                        </h2>
                        <div id='collapse$index' class='accordion-collapse collapse' 
                             data-bs-parent='#categoriesAccordion'>
                            <div class='accordion-body'>
                                {$cat['pages']}
                            </div>
                        </div>
                      </div>";
            }
            echo "</div>";
        }
        
        // اختبار الدوال
        echo "<hr>";
        echo "<h5><i class='fas fa-code me-2'></i>اختبار الدوال</h5>";
        
        if (file_exists('includes/employee-auth.php')) {
            require_once 'includes/employee-auth.php';
            
            $functions = ['employeeCanAccessPage', 'getEmployeeCustomPermissions'];
            foreach ($functions as $func) {
                if (function_exists($func)) {
                    echo "<p class='text-success'><i class='fas fa-check me-2'></i>دالة $func متاحة</p>";
                } else {
                    echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>دالة $func غير متاحة</p>";
                }
            }
        } else {
            echo "<p class='text-warning'><i class='fas fa-exclamation-triangle me-2'></i>ملف employee-auth.php غير موجود</p>";
        }
        
        echo "<div class='alert alert-success mt-4'>
                <h6><i class='fas fa-thumbs-up me-2'></i>النظام جاهز للاستخدام!</h6>
                <p>يمكنك الآن تحديد الصفحات التي يستطيع كل موظف الوصول إليها.</p>
              </div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h6><i class='fas fa-exclamation-circle me-2'></i>خطأ</h6>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "                </div>
                <div class='card-footer text-center'>
                    <a href='employees.php' class='btn btn-primary me-2'>
                        <i class='fas fa-users me-2'></i>إدارة الموظفين
                    </a>
                    <a href='test_employee_pages.php' class='btn btn-info me-2'>
                        <i class='fas fa-search me-2'></i>اختبار مفصل
                    </a>
                    <a href='setup_permissions_quick.php' class='btn btn-warning'>
                        <i class='fas fa-cogs me-2'></i>إعداد النظام
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
