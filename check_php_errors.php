<?php
/**
 * فحص أخطاء PHP في ملف تسجيل دخول الموظف
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص أخطاء PHP</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        .error-box { background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 10px; margin: 10px 0; }
        .success-box { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 10px; margin: 10px 0; }
        .warning-box { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 10px; margin: 10px 0; }
        .code-box { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <div class='row'>
        <div class='col-md-12'>
            <div class='card'>
                <div class='card-header bg-danger text-white'>
                    <h4><i class='fas fa-exclamation-triangle me-2'></i>فحص أخطاء PHP</h4>
                </div>
                <div class='card-body'>";

echo "<h5>1. فحص إعدادات PHP</h5>";
echo "<div class='code-box'>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Error Reporting: " . error_reporting() . "<br>";
echo "Display Errors: " . ini_get('display_errors') . "<br>";
echo "Log Errors: " . ini_get('log_errors') . "<br>";
echo "Error Log: " . ini_get('error_log') . "<br>";
echo "</div>";

echo "<h5>2. فحص الملفات المطلوبة</h5>";

$files_to_check = [
    'config/database.php',
    'client/employee-login.php',
    'client/includes/employee-auth.php',
    'includes/secure_session_middleware.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<div class='success-box'>";
        echo "<i class='fas fa-check-circle me-2'></i>✓ {$file} موجود";
        echo "</div>";
        
        // فحص الأخطاء النحوية
        $output = [];
        $return_var = 0;
        exec("php -l \"{$file}\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<div class='success-box'>";
            echo "<i class='fas fa-check-circle me-2'></i>✓ {$file} لا يحتوي على أخطاء نحوية";
            echo "</div>";
        } else {
            echo "<div class='error-box'>";
            echo "<i class='fas fa-times-circle me-2'></i>✗ {$file} يحتوي على أخطاء نحوية:";
            echo "<pre>" . implode("\n", $output) . "</pre>";
            echo "</div>";
        }
    } else {
        echo "<div class='error-box'>";
        echo "<i class='fas fa-times-circle me-2'></i>✗ {$file} غير موجود";
        echo "</div>";
    }
}

echo "<h5>3. اختبار تضمين الملفات</h5>";

try {
    echo "<div class='warning-box'>";
    echo "<i class='fas fa-info-circle me-2'></i>محاولة تضمين config/database.php...";
    echo "</div>";
    
    require_once 'config/database.php';
    
    echo "<div class='success-box'>";
    echo "<i class='fas fa-check-circle me-2'></i>✓ تم تضمين config/database.php بنجاح";
    echo "</div>";
    
    // اختبار الاتصال بقاعدة البيانات
    if (isset($pdo)) {
        $pdo->query("SELECT 1");
        echo "<div class='success-box'>";
        echo "<i class='fas fa-check-circle me-2'></i>✓ الاتصال بقاعدة البيانات يعمل";
        echo "</div>";
    } else {
        echo "<div class='error-box'>";
        echo "<i class='fas fa-times-circle me-2'></i>✗ متغير \$pdo غير موجود";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error-box'>";
    echo "<i class='fas fa-times-circle me-2'></i>✗ خطأ في تضمين config/database.php: " . $e->getMessage();
    echo "</div>";
}

try {
    echo "<div class='warning-box'>";
    echo "<i class='fas fa-info-circle me-2'></i>محاولة تضمين includes/secure_session_middleware.php...";
    echo "</div>";
    
    require_once 'includes/secure_session_middleware.php';
    
    echo "<div class='success-box'>";
    echo "<i class='fas fa-check-circle me-2'></i>✓ تم تضمين includes/secure_session_middleware.php بنجاح";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error-box'>";
    echo "<i class='fas fa-times-circle me-2'></i>✗ خطأ في تضمين includes/secure_session_middleware.php: " . $e->getMessage();
    echo "</div>";
}

echo "<h5>4. فحص مجلد temp</h5>";

if (is_dir('temp')) {
    echo "<div class='success-box'>";
    echo "<i class='fas fa-check-circle me-2'></i>✓ مجلد temp موجود";
    echo "</div>";
    
    if (is_writable('temp')) {
        echo "<div class='success-box'>";
        echo "<i class='fas fa-check-circle me-2'></i>✓ مجلد temp قابل للكتابة";
        echo "</div>";
    } else {
        echo "<div class='error-box'>";
        echo "<i class='fas fa-times-circle me-2'></i>✗ مجلد temp غير قابل للكتابة";
        echo "</div>";
    }
} else {
    echo "<div class='error-box'>";
    echo "<i class='fas fa-times-circle me-2'></i>✗ مجلد temp غير موجود";
    echo "</div>";
}

echo "<h5>5. محاولة تشغيل كود تسجيل الدخول</h5>";

if (isset($pdo)) {
    try {
        // اختبار الاستعلام
        $stmt = $pdo->prepare("
            SELECT e.*, c.business_name, c.owner_name, c.is_active as client_active
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.username = ? AND e.is_active = 1 AND c.is_active = 1
            LIMIT 1
        ");
        $stmt->execute(['test']);
        
        echo "<div class='success-box'>";
        echo "<i class='fas fa-check-circle me-2'></i>✓ استعلام تسجيل الدخول يعمل بشكل صحيح";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error-box'>";
        echo "<i class='fas fa-times-circle me-2'></i>✗ خطأ في استعلام تسجيل الدخول: " . $e->getMessage();
        echo "</div>";
    }
}

echo "<h5>6. فحص سجل الأخطاء</h5>";

$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    $recent_errors = array_slice(file($error_log_file), -10);
    if (!empty($recent_errors)) {
        echo "<div class='warning-box'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>آخر 10 أخطاء من سجل PHP:";
        echo "<pre>" . implode("", $recent_errors) . "</pre>";
        echo "</div>";
    } else {
        echo "<div class='success-box'>";
        echo "<i class='fas fa-check-circle me-2'></i>✓ لا توجد أخطاء حديثة في السجل";
        echo "</div>";
    }
} else {
    echo "<div class='warning-box'>";
    echo "<i class='fas fa-info-circle me-2'></i>سجل الأخطاء غير متاح أو غير موجود";
    echo "</div>";
}

echo "<h5>7. اختبار مباشر لصفحة تسجيل الدخول</h5>";
echo "<div class='text-center'>";
echo "<a href='client/employee-login.php' class='btn btn-primary me-2' target='_blank'>";
echo "<i class='fas fa-external-link-alt me-1'></i>فتح صفحة تسجيل دخول الموظف";
echo "</a>";
echo "<a href='simple_employee_login_test.php' class='btn btn-success me-2' target='_blank'>";
echo "<i class='fas fa-vial me-1'></i>اختبار تسجيل الدخول المبسط";
echo "</a>";
echo "</div>";

echo "                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
