/**
 * نظام إدارة الإشعارات - PlayGood
 * يدير الإشعارات في جميع صفحات النظام
 */

class NotificationManager {
    constructor() {
        this.checkInterval = 30000; // 30 ثانية
        this.soundEnabled = true;
        this.lastCheckTime = new Date();
        this.init();
    }

    init() {
        // طلب إذن الإشعارات من المتصفح
        this.requestNotificationPermission();
        
        // بدء فحص الإشعارات الدوري
        this.startPeriodicCheck();
        
        // فحص أولي عند تحميل الصفحة
        this.checkNotifications();
        
        // إضافة مستمعي الأحداث
        this.addEventListeners();
    }

    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                console.log('إذن الإشعارات:', permission);
            });
        }
    }

    startPeriodicCheck() {
        setInterval(() => {
            this.checkNotifications();
        }, this.checkInterval);
    }

    async checkNotifications() {
        try {
            const response = await fetch('api/check_notifications.php');
            const data = await response.json();
            
            if (data.success) {
                this.updateNotificationBadge(data.unread_count);
                
                // إذا كانت هناك إشعارات جديدة
                if (data.new_count > 0) {
                    this.handleNewNotifications(data.new_notifications);
                }
                
                // إذا كانت هناك إشعارات عاجلة
                if (data.urgent_count > 0) {
                    this.handleUrgentNotifications(data.urgent_count);
                }
            }
        } catch (error) {
            console.error('خطأ في فحص الإشعارات:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.getElementById('notification-count');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'inline' : 'none';
            
            // تحديث عنوان الصفحة
            if (count > 0) {
                document.title = `(${count}) ${document.title.replace(/^\(\d+\)\s*/, '')}`;
            } else {
                document.title = document.title.replace(/^\(\d+\)\s*/, '');
            }
        }
    }

    handleNewNotifications(notifications) {
        notifications.forEach(notification => {
            // تشغيل صوت الإشعار
            if (this.soundEnabled) {
                this.playNotificationSound();
            }
            
            // إظهار إشعار في المتصفح
            this.showBrowserNotification(notification);
            
            // إظهار إشعار داخل الصفحة
            this.showInPageNotification(notification);
        });
    }

    handleUrgentNotifications(urgentCount) {
        // تغيير لون أيقونة الإشعارات للإشعارات العاجلة
        const notificationLink = document.getElementById('notifications-link');
        if (notificationLink && urgentCount > 0) {
            notificationLink.classList.add('urgent-notifications');
            
            // إضافة تأثير وميض
            this.addBlinkEffect(notificationLink);
        }
    }

    playNotificationSound() {
        try {
            // يمكن إضافة ملف صوتي مخصص
            const audio = new Audio('assets/sounds/notification.mp3');
            audio.volume = 0.5;
            audio.play().catch(e => {
                // استخدام صوت النظام الافتراضي
                console.log('استخدام صوت النظام الافتراضي');
            });
        } catch (e) {
            console.log('لا يمكن تشغيل صوت الإشعار');
        }
    }

    showBrowserNotification(notification) {
        if (Notification.permission === 'granted') {
            const options = {
                body: notification.message,
                icon: 'assets/images/notification-icon.png',
                badge: 'assets/images/badge-icon.png',
                tag: `notification-${notification.notification_id}`,
                requireInteraction: notification.priority === 'urgent',
                actions: notification.action_url ? [
                    {
                        action: 'view',
                        title: 'عرض',
                        icon: 'assets/images/view-icon.png'
                    }
                ] : []
            };

            const browserNotification = new Notification(notification.title, options);
            
            browserNotification.onclick = () => {
                window.focus();
                if (notification.action_url) {
                    window.location.href = notification.action_url;
                } else {
                    window.location.href = 'notifications.php';
                }
                browserNotification.close();
            };

            // إغلاق تلقائي بعد 10 ثوان (إلا إذا كان عاجل)
            if (notification.priority !== 'urgent') {
                setTimeout(() => {
                    browserNotification.close();
                }, 10000);
            }
        }
    }

    showInPageNotification(notification) {
        // إنشاء إشعار داخل الصفحة
        const notificationHtml = `
            <div class="alert alert-${this.getPriorityClass(notification.priority)} alert-dismissible fade show notification-toast" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-${this.getPriorityIcon(notification.priority)} me-2"></i>
                    <div class="flex-grow-1">
                        <strong>${notification.title}</strong>
                        <div class="small">${notification.message}</div>
                    </div>
                    ${notification.action_url ? `
                        <a href="${notification.action_url}" class="btn btn-sm btn-outline-primary me-2">
                            عرض
                        </a>
                    ` : ''}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        `;

        // إضافة الإشعار إلى الصفحة
        this.addToastToPage(notificationHtml);
    }

    addToastToPage(html) {
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        const toastElement = document.createElement('div');
        toastElement.innerHTML = html;
        toastContainer.appendChild(toastElement.firstElementChild);

        // إزالة الإشعار تلقائياً بعد 8 ثوان
        setTimeout(() => {
            const toast = toastContainer.querySelector('.notification-toast');
            if (toast) {
                toast.remove();
            }
        }, 8000);
    }

    getPriorityClass(priority) {
        const classes = {
            'urgent': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'secondary'
        };
        return classes[priority] || 'info';
    }

    getPriorityIcon(priority) {
        const icons = {
            'urgent': 'exclamation-triangle',
            'high': 'exclamation-circle',
            'medium': 'info-circle',
            'low': 'circle'
        };
        return icons[priority] || 'bell';
    }

    addBlinkEffect(element) {
        element.style.animation = 'blink 1s infinite';
        
        // إزالة التأثير بعد 10 ثوان
        setTimeout(() => {
            element.style.animation = '';
            element.classList.remove('urgent-notifications');
        }, 10000);
    }

    addEventListeners() {
        // مستمع لتغيير حالة النافذة (focus/blur)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // عند العودة للصفحة، فحص الإشعارات
                this.checkNotifications();
            }
        });

        // مستمع لأحداث الشبكة
        window.addEventListener('online', () => {
            console.log('الاتصال متاح - فحص الإشعارات');
            this.checkNotifications();
        });

        window.addEventListener('offline', () => {
            console.log('لا يوجد اتصال بالإنترنت');
        });
    }

    // دوال مساعدة للتحكم في الإشعارات
    enableSound() {
        this.soundEnabled = true;
        localStorage.setItem('notificationSound', 'enabled');
    }

    disableSound() {
        this.soundEnabled = false;
        localStorage.setItem('notificationSound', 'disabled');
    }

    setCheckInterval(seconds) {
        this.checkInterval = seconds * 1000;
        // إعادة تشغيل الفحص الدوري
        clearInterval(this.intervalId);
        this.startPeriodicCheck();
    }
}

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }
    
    .urgent-notifications {
        color: #dc3545 !important;
    }
    
    .notification-toast {
        min-width: 300px;
        max-width: 400px;
    }
    
    .notification-badge {
        font-size: 0.7em;
        margin-right: 5px;
    }
`;
document.head.appendChild(style);

// تشغيل نظام الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من إعدادات الصوت المحفوظة
    const soundSetting = localStorage.getItem('notificationSound');
    
    // إنشاء مدير الإشعارات
    window.notificationManager = new NotificationManager();
    
    if (soundSetting === 'disabled') {
        window.notificationManager.disableSound();
    }
    
    console.log('تم تشغيل نظام الإشعارات');
});
