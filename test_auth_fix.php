<?php
/**
 * اختبار إصلاح ملف المصادقة
 * هذا الملف يختبر أن ملف auth.php يدعم كلاً من العملاء والموظفين
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح ملف المصادقة</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        .test-card { margin: 20px 0; }
        .test-success { color: #28a745; }
        .test-error { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-info { color: #17a2b8; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card test-card'>
                <div class='card-header bg-success text-white'>
                    <h4><i class='fas fa-shield-alt me-2'></i>اختبار إصلاح ملف المصادقة</h4>
                    <p class='mb-0'>فحص ملف client/includes/auth.php للتأكد من دعم العملاء والموظفين</p>
                </div>
                <div class='card-body'>";

// فحص وجود الملف
if (!file_exists('client/includes/auth.php')) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "ملف auth.php غير موجود!";
    echo "</div>";
    exit;
}

$auth_content = file_get_contents('client/includes/auth.php');

echo "<h5><i class='fas fa-search me-2'></i>فحص محتوى الملف:</h5>";

// فحص التحقق من تسجيل الدخول
echo "<div class='card mb-3'>";
echo "<div class='card-header'>";
echo "<h6><i class='fas fa-sign-in-alt me-2'></i>فحص التحقق من تسجيل الدخول</h6>";
echo "</div>";
echo "<div class='card-body'>";

if (strpos($auth_content, '!isset($_SESSION[\'client_id\']) && !isset($_SESSION[\'employee_id\'])') !== false) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "✓ يدعم التحقق من كلاً من العملاء والموظفين";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i>";
    echo "✗ لا يدعم التحقق من الموظفين";
    echo "</div>";
}

if (strpos($auth_content, 'employee-login.php') !== false) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "✓ يحتوي على توجيه لصفحة تسجيل دخول الموظف";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i>";
    echo "✗ لا يحتوي على توجيه لصفحة تسجيل دخول الموظف";
    echo "</div>";
}

echo "</div></div>";

// فحص دالة hasPagePermission
echo "<div class='card mb-3'>";
echo "<div class='card-header'>";
echo "<h6><i class='fas fa-key me-2'></i>فحص دالة hasPagePermission</h6>";
echo "</div>";
echo "<div class='card-body'>";

if (strpos($auth_content, 'isset($_SESSION[\'employee_id\'])') !== false) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "✓ تدعم فحص صلاحيات الموظفين";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "⚠ قد لا تدعم فحص صلاحيات الموظفين";
    echo "</div>";
}

if (strpos($auth_content, 'employeeHasPermission') !== false) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "✓ تستخدم دالة employeeHasPermission";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "⚠ لا تستخدم دالة employeeHasPermission";
    echo "</div>";
}

echo "</div></div>";

// فحص دالة getAllowedPages
echo "<div class='card mb-3'>";
echo "<div class='card-header'>";
echo "<h6><i class='fas fa-list me-2'></i>فحص دالة getAllowedPages</h6>";
echo "</div>";
echo "<div class='card-body'>";

if (strpos($auth_content, 'function getAllowedPages()') !== false) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "✓ دالة getAllowedPages موجودة";
    echo "</div>";
    
    if (strpos($auth_content, 'إذا كان موظف، إرجاع الصفحات المسموحة له') !== false) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "✓ تدعم إرجاع الصفحات المسموحة للموظفين";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "⚠ قد لا تدعم الموظفين في getAllowedPages";
        echo "</div>";
    }
} else {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i>";
    echo "✗ دالة getAllowedPages غير موجودة";
    echo "</div>";
}

echo "</div></div>";

// فحص الصفحات المستثناة
echo "<div class='card mb-3'>";
echo "<div class='card-header'>";
echo "<h6><i class='fas fa-ban me-2'></i>فحص الصفحات المستثناة</h6>";
echo "</div>";
echo "<div class='card-body'>";

if (strpos($auth_content, 'employee-login') !== false) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "✓ صفحة employee-login مستثناة من فحص الصلاحيات";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "⚠ صفحة employee-login قد لا تكون مستثناة";
    echo "</div>";
}

echo "</div></div>";

// عرض ملخص الإصلاحات
echo "<div class='alert alert-info mt-4'>";
echo "<h5><i class='fas fa-info-circle me-2'></i>ملخص الإصلاحات المطبقة:</h5>";
echo "<ul>";
echo "<li>✅ تم تحديث التحقق من تسجيل الدخول ليدعم كلاً من العملاء والموظفين</li>";
echo "<li>✅ تم إضافة التوجيه الذكي لصفحة تسجيل الدخول المناسبة</li>";
echo "<li>✅ تم تحديث دالة hasPagePermission لدعم صلاحيات الموظفين</li>";
echo "<li>✅ تم تحديث دالة getAllowedPages لإرجاع الصفحات المسموحة للموظفين</li>";
echo "<li>✅ تم إضافة صفحة employee-login للصفحات المستثناة</li>";
echo "<li>✅ تم ربط النظام بدوال صلاحيات الموظفين</li>";
echo "</ul>";
echo "</div>";

// تعليمات الاختبار
echo "<div class='alert alert-primary mt-4'>";
echo "<h5><i class='fas fa-clipboard-list me-2'></i>كيفية اختبار الإصلاح:</h5>";
echo "<ol>";
echo "<li>قم بتسجيل الخروج من أي جلسة حالية</li>";
echo "<li>حاول الوصول إلى صفحة تتطلب ملف auth.php (مثل customers.php)</li>";
echo "<li>يجب أن يتم توجيهك إلى صفحة تسجيل الدخول المناسبة</li>";
echo "<li>سجل دخول كموظف وتأكد من ظهور الصفحات المسموحة له فقط</li>";
echo "<li>سجل دخول كعميل وتأكد من عمل النظام كما هو معتاد</li>";
echo "</ol>";
echo "</div>";

// روابط للاختبار
echo "<div class='card mt-4'>";
echo "<div class='card-header'>";
echo "<h5><i class='fas fa-external-link-alt me-2'></i>روابط للاختبار</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>صفحات تستخدم auth.php:</h6>";
echo "<a href='client/customers.php' class='btn btn-outline-primary btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-users me-1'></i>العملاء";
echo "</a>";
echo "<a href='client/sessions.php' class='btn btn-outline-info btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-clock me-1'></i>الجلسات";
echo "</a>";
echo "<a href='client/cafeteria.php' class='btn btn-outline-warning btn-sm mb-2' target='_blank'>";
echo "<i class='fas fa-coffee me-1'></i>الكافتيريا";
echo "</a>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6>صفحات تسجيل الدخول:</h6>";
echo "<a href='client/login.php' class='btn btn-outline-success btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-user me-1'></i>دخول العميل";
echo "</a>";
echo "<a href='client/employee-login.php' class='btn btn-outline-danger btn-sm mb-2' target='_blank'>";
echo "<i class='fas fa-user-tie me-1'></i>دخول الموظف";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "            </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
