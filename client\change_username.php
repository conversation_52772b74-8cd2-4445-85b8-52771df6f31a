<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

// التحقق من الصلاحيات - فقط المديرين يمكنهم إدارة الموظفين
if (isset($_SESSION['employee_id']) && !employeeHasPermission('manage_employees')) {
    header('Location: dashboard.php?error=no_permission');
    exit;
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

// التحقق من وجود معرف الموظف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: employees.php?error=invalid_employee');
    exit;
}

$employee_id = (int)$_GET['id'];

// جلب بيانات الموظف
try {
    $employee_query = $pdo->prepare("
        SELECT * FROM employees 
        WHERE id = ? AND client_id = ?
    ");
    $employee_query->execute([$employee_id, $client_id]);
    $employee = $employee_query->fetch();
    
    if (!$employee) {
        header('Location: employees.php?error=employee_not_found');
        exit;
    }
} catch (PDOException $e) {
    header('Location: employees.php?error=database_error');
    exit;
}

// معالجة تغيير اسم المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_username'])) {
    $new_username = trim($_POST['new_username']);
    $confirm_password = $_POST['confirm_password'];
    
    // التحقق من كلمة المرور للتأكيد
    $current_user_password = '';
    if (isset($_SESSION['employee_id'])) {
        // موظف يغير اسم مستخدم موظف آخر - التحقق من كلمة مرور الموظف الحالي
        $stmt = $pdo->prepare("SELECT password_hash FROM employees WHERE id = ?");
        $stmt->execute([$_SESSION['employee_id']]);
        $current_user = $stmt->fetch();
        $current_user_password = $current_user['password_hash'];
    } else {
        // عميل يغير اسم مستخدم موظف - التحقق من كلمة مرور العميل
        $stmt = $pdo->prepare("SELECT password_hash FROM clients WHERE client_id = ?");
        $stmt->execute([$client_id]);
        $current_user = $stmt->fetch();
        $current_user_password = $current_user['password_hash'];
    }
    
    if (empty($new_username)) {
        $_SESSION['error'] = "يرجى إدخال اسم المستخدم الجديد";
    } elseif (strlen($new_username) < 3) {
        $_SESSION['error'] = "اسم المستخدم يجب أن يكون 3 أحرف على الأقل";
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $new_username)) {
        $_SESSION['error'] = "اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط";
    } elseif (empty($confirm_password)) {
        $_SESSION['error'] = "يرجى إدخال كلمة المرور للتأكيد";
    } elseif (!password_verify($confirm_password, $current_user_password)) {
        $_SESSION['error'] = "كلمة المرور غير صحيحة";
    } else {
        try {
            // التحقق من عدم وجود اسم المستخدم مسبقاً
            $stmt = $pdo->prepare("
                SELECT id FROM employees 
                WHERE username = ? AND client_id = ? AND id != ?
            ");
            $stmt->execute([$new_username, $client_id, $employee_id]);
            
            if ($stmt->fetch()) {
                $_SESSION['error'] = "اسم المستخدم موجود مسبقاً، يرجى اختيار اسم آخر";
            } else {
                // تحديث اسم المستخدم
                $stmt = $pdo->prepare("
                    UPDATE employees 
                    SET username = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ? AND client_id = ?
                ");
                $stmt->execute([$new_username, $employee_id, $client_id]);
                
                $_SESSION['success'] = "تم تغيير اسم المستخدم بنجاح من '{$employee['username']}' إلى '{$new_username}'";
                header('Location: employees.php');
                exit;
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = "حدث خطأ أثناء تغيير اسم المستخدم: " . $e->getMessage();
        }
    }
}

$page_title = "تغيير اسم المستخدم (صفحة منفصلة) - " . $employee['name'];
$active_page = "employees";

require_once 'includes/header.php';
?>

<!-- تحميل ملف CSS المخصص -->
<link rel="stylesheet" href="assets/css/permissions.css">

<?php
?>

<style>
.employee-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e63946 100%);
    color: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.employee-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
}

@keyframes float {
    0% { transform: translateX(-100px) translateY(-100px); }
    100% { transform: translateX(100px) translateY(100px); }
}

.employee-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-right: 20px;
}

.form-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 25px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #dee2e6;
}

.section-body {
    padding: 25px;
}

.save-section {
    position: sticky;
    bottom: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
    padding: 20px;
    margin-top: 30px;
}

.security-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .employee-header {
        text-align: center;
    }
    
    .employee-avatar {
        margin: 0 auto 15px;
    }
}
</style>

<div class="container-fluid py-4">
    <!-- مؤشر الصفحة المنفصلة -->
    <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class="fas fa-external-link-alt fa-2x me-3"></i>
            <div>
                <h6 class="alert-heading mb-1">
                    <i class="fas fa-star me-1"></i>صفحة تغيير اسم المستخدم المنفصلة
                </h6>
                <p class="mb-0">
                    تم تطوير هذه الصفحة لتكون منفصلة مع إجراءات أمان إضافية لحماية بيانات تسجيل الدخول
                </p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- رأس الصفحة مع معلومات الموظف -->
    <div class="employee-header">
        <div class="d-flex align-items-center">
            <div class="employee-avatar">
                <i class="fas fa-user-edit"></i>
            </div>
            <div class="flex-grow-1">
                <h2 class="mb-2">
                    <i class="fas fa-key me-2"></i>
                    تغيير اسم المستخدم: <?php echo htmlspecialchars($employee['name']); ?>
                </h2>
                <p class="mb-2 opacity-75">
                    <i class="fas fa-shield-alt me-1"></i>
                    صفحة آمنة لتغيير اسم المستخدم مع التحقق من الصلاحيات
                </p>
                <div class="row">
                    <div class="col-md-4">
                        <small class="opacity-75">اسم المستخدم الحالي:</small><br>
                        <span class="badge bg-light text-dark fs-6">
                            <?php echo htmlspecialchars($employee['username']); ?>
                        </span>
                    </div>
                    <div class="col-md-4">
                        <small class="opacity-75">الوظيفة:</small><br>
                        <span><?php echo match($employee['role']) {
                            'manager' => 'مدير',
                            'cashier' => 'كاشير', 
                            'waiter' => 'ويتر',
                            'cleaner' => 'عامل نظافة',
                            default => $employee['role']
                        }; ?></span>
                    </div>
                    <div class="col-md-4">
                        <small class="opacity-75">آخر تسجيل دخول:</small><br>
                        <span><?php echo $employee['last_login'] ? date('Y-m-d H:i', strtotime($employee['last_login'])) : 'لم يسجل دخول بعد'; ?></span>
                    </div>
                </div>
            </div>
            <div>
                <a href="edit_employee.php?id=<?php echo $employee_id; ?>" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>العودة لتعديل الموظف
                </a>
                <div class="mt-2">
                    <small class="text-light opacity-75">
                        <i class="fas fa-external-link-alt me-1"></i>
                        صفحة منفصلة آمنة
                    </small>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="changeUsernameForm">
        <input type="hidden" name="employee_id" value="<?php echo $employee_id; ?>">
        
        <!-- تحذير أمني -->
        <div class="security-warning">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                <div>
                    <h6 class="mb-1">
                        <i class="fas fa-shield-alt me-1"></i>تحذير أمني مهم
                    </h6>
                    <ul class="mb-0 small">
                        <li>تغيير اسم المستخدم سيتطلب من الموظف تسجيل الدخول مرة أخرى</li>
                        <li>يجب إدخال كلمة المرور الخاصة بك للتأكيد</li>
                        <li>اسم المستخدم يجب أن يكون فريداً ولا يحتوي على مسافات</li>
                        <li>سيتم حفظ سجل بالتغيير لأغراض الأمان</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- قسم تغيير اسم المستخدم -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>تغيير اسم المستخدم
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-user me-1"></i>اسم المستخدم الحالي
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-secondary text-white">
                                    <i class="fas fa-user-circle"></i>
                                </span>
                                <input type="text" class="form-control bg-light"
                                       value="<?php echo htmlspecialchars($employee['username']); ?>" readonly>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                هذا هو اسم المستخدم الحالي للموظف
                            </small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-edit me-1"></i>اسم المستخدم الجديد <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="fas fa-user-plus"></i>
                                </span>
                                <input type="text" name="new_username" class="form-control"
                                       placeholder="أدخل اسم المستخدم الجديد" required
                                       pattern="[a-zA-Z0-9_]+" minlength="3" maxlength="50">
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                أحرف وأرقام فقط، بدون مسافات، 3-50 حرف
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التأكيد الأمني -->
        <div class="form-section">
            <div class="section-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>التأكيد الأمني
                </h5>
            </div>
            <div class="section-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                <?php if (isset($_SESSION['employee_id'])): ?>
                                    كلمة المرور الخاصة بك (كموظف مدير)
                                <?php else: ?>
                                    كلمة المرور الخاصة بك (كعميل)
                                <?php endif; ?>
                                <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-danger text-white">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input type="password" name="confirm_password" class="form-control"
                                       placeholder="أدخل كلمة المرور للتأكيد" required>
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                مطلوب لتأكيد هويتك قبل تغيير اسم المستخدم
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info h-100 d-flex align-items-center">
                            <div>
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <h6 class="mb-1">لماذا كلمة المرور؟</h6>
                                <small>
                                    لحماية بيانات الموظفين من التغيير غير المصرح به
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الحفظ -->
        <div class="save-section">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">
                        <i class="fas fa-save me-2"></i>تأكيد تغيير اسم المستخدم
                    </h6>
                    <small class="text-muted">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>تحذير:</strong> سيحتاج الموظف لتسجيل الدخول مرة أخرى بعد التغيير
                        <br>
                        <i class="fas fa-check-circle me-1"></i>
                        <strong>مزايا الصفحة المنفصلة:</strong> أمان إضافي وتحقق شامل
                    </small>
                </div>
                <div>
                    <a href="edit_employee.php?id=<?php echo $employee_id; ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <button type="submit" name="change_username" class="btn btn-warning btn-lg">
                        <i class="fas fa-user-edit me-2"></i>تغيير اسم المستخدم
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// تبديل إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordInput = document.querySelector('input[name="confirm_password"]');
    const toggleIcon = document.getElementById('passwordToggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// التحقق من صحة اسم المستخدم أثناء الكتابة
document.querySelector('input[name="new_username"]').addEventListener('input', function() {
    const username = this.value;
    const pattern = /^[a-zA-Z0-9_]+$/;

    if (username.length > 0 && !pattern.test(username)) {
        this.setCustomValidity('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط');
    } else if (username.length > 0 && username.length < 3) {
        this.setCustomValidity('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
    } else {
        this.setCustomValidity('');
    }
});

// تأكيد قبل الإرسال
document.getElementById('changeUsernameForm').addEventListener('submit', function(e) {
    const newUsername = document.querySelector('input[name="new_username"]').value;
    const currentUsername = '<?php echo htmlspecialchars($employee['username']); ?>';

    if (newUsername === currentUsername) {
        e.preventDefault();
        alert('اسم المستخدم الجديد مطابق للحالي. يرجى إدخال اسم مختلف.');
        return false;
    }

    if (!confirm(`هل أنت متأكد من تغيير اسم المستخدم من "${currentUsername}" إلى "${newUsername}"؟\n\nسيحتاج الموظف لتسجيل الدخول مرة أخرى.`)) {
        e.preventDefault();
        return false;
    }
});

// إخفاء الرسائل تلقائياً بعد 5 ثوان
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert && alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 5000);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
