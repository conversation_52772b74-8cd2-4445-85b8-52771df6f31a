-- تحسين نظام تسجيل عمليات الشيفت - PlayGood
-- إضافة نظام شامل لتسجيل جميع الأحداث والعمليات

-- 1. إنشاء جدول تسجيل أحداث الشيفت
CREATE TABLE IF NOT EXISTS shift_events (
    event_id INT AUTO_INCREMENT PRIMARY KEY,
    shift_id INT NOT NULL,
    employee_id INT NULL,
    event_type ENUM(
        'shift_start', 'shift_end', 'shift_pause', 'shift_resume',
        'employee_checkin', 'employee_checkout', 'break_start', 'break_end',
        'incident', 'note', 'task_completed', 'issue_reported',
        'equipment_check', 'customer_complaint', 'maintenance',
        'shift_handover', 'emergency', 'other'
    ) NOT NULL,
    event_title VARCHAR(200) NOT NULL,
    event_description TEXT,
    event_data JSON NULL COMMENT 'بيانات إضافية للحدث',
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
    is_resolved BOOLEAN DEFAULT TRUE,
    recorded_by INT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    resolved_by INT NULL,
    resolution_notes TEXT,
    
    FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (recorded_by) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES employees(id) ON DELETE SET NULL,
    
    INDEX idx_shift_events_shift (shift_id),
    INDEX idx_shift_events_employee (employee_id),
    INDEX idx_shift_events_type (event_type),
    INDEX idx_shift_events_date (recorded_at),
    INDEX idx_shift_events_severity (severity)
);

-- 2. إنشاء جدول ملخص الشيفت
CREATE TABLE IF NOT EXISTS shift_summaries (
    summary_id INT AUTO_INCREMENT PRIMARY KEY,
    shift_id INT NOT NULL UNIQUE,
    total_employees_assigned INT DEFAULT 0,
    total_employees_attended INT DEFAULT 0,
    total_work_hours DECIMAL(6,2) DEFAULT 0.00,
    total_overtime_hours DECIMAL(6,2) DEFAULT 0.00,
    total_break_hours DECIMAL(6,2) DEFAULT 0.00,
    total_events INT DEFAULT 0,
    critical_events INT DEFAULT 0,
    unresolved_issues INT DEFAULT 0,
    attendance_percentage DECIMAL(5,2) DEFAULT 0.00,
    performance_score DECIMAL(3,2) DEFAULT 0.00 COMMENT 'نقاط الأداء من 10',
    shift_notes TEXT,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generated_by INT NULL,
    
    FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES employees(id) ON DELETE SET NULL,
    
    INDEX idx_shift_summaries_date (generated_at),
    INDEX idx_shift_summaries_performance (performance_score)
);

-- 3. إنشاء جدول تقييم أداء الموظفين في الشيفت
CREATE TABLE IF NOT EXISTS shift_employee_performance (
    performance_id INT AUTO_INCREMENT PRIMARY KEY,
    shift_id INT NOT NULL,
    employee_id INT NOT NULL,
    attendance_score DECIMAL(3,2) DEFAULT 0.00 COMMENT 'نقاط الحضور من 10',
    punctuality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT 'نقاط الالتزام بالوقت من 10',
    performance_score DECIMAL(3,2) DEFAULT 0.00 COMMENT 'نقاط الأداء العام من 10',
    tasks_completed INT DEFAULT 0,
    issues_reported INT DEFAULT 0,
    customer_feedback_score DECIMAL(3,2) DEFAULT 0.00,
    supervisor_notes TEXT,
    total_score DECIMAL(3,2) DEFAULT 0.00 COMMENT 'المجموع الكلي من 10',
    evaluated_by INT NULL,
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (evaluated_by) REFERENCES employees(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_shift_employee (shift_id, employee_id),
    INDEX idx_performance_shift (shift_id),
    INDEX idx_performance_employee (employee_id),
    INDEX idx_performance_score (total_score)
);

-- 4. إنشاء جدول مهام الشيفت
CREATE TABLE IF NOT EXISTS shift_tasks (
    task_id INT AUTO_INCREMENT PRIMARY KEY,
    shift_id INT NOT NULL,
    task_title VARCHAR(200) NOT NULL,
    task_description TEXT,
    assigned_to INT NULL,
    task_priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    task_status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    due_time TIME NULL,
    estimated_duration INT DEFAULT 0 COMMENT 'المدة المتوقعة بالدقائق',
    actual_duration INT DEFAULT 0 COMMENT 'المدة الفعلية بالدقائق',
    completion_notes TEXT,
    created_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES employees(id) ON DELETE SET NULL,
    
    INDEX idx_shift_tasks_shift (shift_id),
    INDEX idx_shift_tasks_employee (assigned_to),
    INDEX idx_shift_tasks_status (task_status),
    INDEX idx_shift_tasks_priority (task_priority)
);

-- 5. إضافة حقول جديدة لجدول الشيفتات الموجود
ALTER TABLE shifts 
ADD COLUMN IF NOT EXISTS actual_start_time TIMESTAMP NULL COMMENT 'وقت البداية الفعلي',
ADD COLUMN IF NOT EXISTS actual_end_time TIMESTAMP NULL COMMENT 'وقت النهاية الفعلي',
ADD COLUMN IF NOT EXISTS shift_supervisor INT NULL COMMENT 'مشرف الشيفت',
ADD COLUMN IF NOT EXISTS handover_notes TEXT COMMENT 'ملاحظات التسليم',
ADD COLUMN IF NOT EXISTS is_summary_generated BOOLEAN DEFAULT FALSE COMMENT 'هل تم إنشاء الملخص',
ADD FOREIGN KEY (shift_supervisor) REFERENCES employees(id) ON DELETE SET NULL;

-- 6. إنشاء view شامل لتفاصيل الشيفت
CREATE OR REPLACE VIEW shift_detailed_view AS
SELECT 
    s.*,
    ss.total_employees_assigned,
    ss.total_employees_attended,
    ss.total_work_hours,
    ss.total_overtime_hours,
    ss.attendance_percentage,
    ss.performance_score,
    ss.total_events,
    ss.critical_events,
    ss.unresolved_issues,
    supervisor.name as supervisor_name,
    COUNT(DISTINCT es.employee_id) as assigned_count,
    COUNT(DISTINCT sa.employee_id) as attended_count,
    COUNT(DISTINCT se.event_id) as events_count,
    COUNT(DISTINCT CASE WHEN se.severity = 'critical' THEN se.event_id END) as critical_events_count
FROM shifts s
LEFT JOIN shift_summaries ss ON s.shift_id = ss.shift_id
LEFT JOIN employees supervisor ON s.shift_supervisor = supervisor.id
LEFT JOIN employee_shifts es ON s.shift_id = es.shift_id
LEFT JOIN shift_attendance sa ON s.shift_id = sa.shift_id
LEFT JOIN shift_events se ON s.shift_id = se.shift_id
GROUP BY s.shift_id;

-- 7. إنشاء stored procedure لحساب ملخص الشيفت
DELIMITER //
CREATE OR REPLACE PROCEDURE GenerateShiftSummary(IN p_shift_id INT)
BEGIN
    DECLARE v_total_assigned INT DEFAULT 0;
    DECLARE v_total_attended INT DEFAULT 0;
    DECLARE v_total_work_hours DECIMAL(6,2) DEFAULT 0.00;
    DECLARE v_total_overtime DECIMAL(6,2) DEFAULT 0.00;
    DECLARE v_total_break_hours DECIMAL(6,2) DEFAULT 0.00;
    DECLARE v_total_events INT DEFAULT 0;
    DECLARE v_critical_events INT DEFAULT 0;
    DECLARE v_unresolved_issues INT DEFAULT 0;
    DECLARE v_attendance_percentage DECIMAL(5,2) DEFAULT 0.00;
    DECLARE v_performance_score DECIMAL(3,2) DEFAULT 0.00;
    
    -- حساب عدد الموظفين المخصصين
    SELECT COUNT(*) INTO v_total_assigned
    FROM employee_shifts WHERE shift_id = p_shift_id;
    
    -- حساب عدد الموظفين الحاضرين
    SELECT COUNT(DISTINCT employee_id) INTO v_total_attended
    FROM shift_attendance WHERE shift_id = p_shift_id AND check_in_time IS NOT NULL;
    
    -- حساب إجمالي ساعات العمل
    SELECT 
        COALESCE(SUM(actual_hours), 0),
        COALESCE(SUM(overtime_hours), 0),
        COALESCE(SUM(break_hours), 0)
    INTO v_total_work_hours, v_total_overtime, v_total_break_hours
    FROM shift_attendance WHERE shift_id = p_shift_id;
    
    -- حساب الأحداث
    SELECT 
        COUNT(*),
        COUNT(CASE WHEN severity = 'critical' THEN 1 END),
        COUNT(CASE WHEN is_resolved = FALSE THEN 1 END)
    INTO v_total_events, v_critical_events, v_unresolved_issues
    FROM shift_events WHERE shift_id = p_shift_id;
    
    -- حساب نسبة الحضور
    IF v_total_assigned > 0 THEN
        SET v_attendance_percentage = (v_total_attended * 100.0) / v_total_assigned;
    END IF;
    
    -- حساب نقاط الأداء (متوسط نقاط الموظفين)
    SELECT COALESCE(AVG(total_score), 0) INTO v_performance_score
    FROM shift_employee_performance WHERE shift_id = p_shift_id;
    
    -- إدراج أو تحديث الملخص
    INSERT INTO shift_summaries (
        shift_id, total_employees_assigned, total_employees_attended,
        total_work_hours, total_overtime_hours, total_break_hours,
        total_events, critical_events, unresolved_issues,
        attendance_percentage, performance_score
    ) VALUES (
        p_shift_id, v_total_assigned, v_total_attended,
        v_total_work_hours, v_total_overtime, v_total_break_hours,
        v_total_events, v_critical_events, v_unresolved_issues,
        v_attendance_percentage, v_performance_score
    ) ON DUPLICATE KEY UPDATE
        total_employees_assigned = v_total_assigned,
        total_employees_attended = v_total_attended,
        total_work_hours = v_total_work_hours,
        total_overtime_hours = v_total_overtime,
        total_break_hours = v_total_break_hours,
        total_events = v_total_events,
        critical_events = v_critical_events,
        unresolved_issues = v_unresolved_issues,
        attendance_percentage = v_attendance_percentage,
        performance_score = v_performance_score,
        generated_at = CURRENT_TIMESTAMP;
    
    -- تحديث حالة الشيفت
    UPDATE shifts SET is_summary_generated = TRUE WHERE shift_id = p_shift_id;
    
END //
DELIMITER ;

-- 8. إنشاء trigger لتسجيل الأحداث تلقائياً
DELIMITER //
CREATE OR REPLACE TRIGGER shift_attendance_events
AFTER INSERT ON shift_attendance
FOR EACH ROW
BEGIN
    -- تسجيل حدث تسجيل الحضور
    IF NEW.check_in_time IS NOT NULL THEN
        INSERT INTO shift_events (
            shift_id, employee_id, event_type, event_title, 
            event_description, recorded_by, recorded_at
        ) VALUES (
            NEW.shift_id, NEW.employee_id, 'employee_checkin', 
            'تسجيل حضور موظف',
            CONCAT('تم تسجيل حضور الموظف في الوقت: ', NEW.check_in_time),
            NEW.recorded_by, NEW.check_in_time
        );
    END IF;
END //
DELIMITER ;

-- 9. إضافة صلاحيات جديدة للنظام
INSERT IGNORE INTO permissions (permission_name, permission_label, permission_description, category) VALUES
('manage_shift_events', 'إدارة أحداث الشيفت', 'تسجيل وإدارة أحداث الشيفت', 'shifts'),
('view_shift_summaries', 'عرض ملخصات الشيفت', 'عرض ملخصات وتقارير الشيفت', 'shifts'),
('evaluate_employees', 'تقييم الموظفين', 'تقييم أداء الموظفين في الشيفت', 'shifts'),
('manage_shift_tasks', 'إدارة مهام الشيفت', 'إنشاء وإدارة مهام الشيفت', 'shifts'),
('generate_shift_reports', 'إنشاء تقارير الشيفت', 'إنشاء التقارير التلقائية للشيفت', 'shifts');

-- 10. إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS admin_notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    notification_type ENUM(
        'shift_completed', 'shift_summary_ready', 'critical_event',
        'unresolved_issues', 'attendance_alert', 'performance_alert',
        'system_alert', 'maintenance_reminder', 'other'
    ) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    related_id INT NULL COMMENT 'معرف العنصر المرتبط (shift_id, event_id, etc)',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500) NULL COMMENT 'رابط الإجراء المطلوب',
    action_label VARCHAR(100) NULL COMMENT 'نص زر الإجراء',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    dismissed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL COMMENT 'تاريخ انتهاء صلاحية الإشعار',

    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,

    INDEX idx_notifications_client (client_id),
    INDEX idx_notifications_type (notification_type),
    INDEX idx_notifications_priority (priority),
    INDEX idx_notifications_status (is_read, is_dismissed),
    INDEX idx_notifications_date (created_at)
);

-- 11. إنشاء trigger لإنشاء إشعارات تلقائية عند اكتمال الشيفت
DELIMITER //
CREATE OR REPLACE TRIGGER shift_completion_notification
AFTER UPDATE ON shifts
FOR EACH ROW
BEGIN
    -- إشعار عند اكتمال الشيفت
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        INSERT INTO admin_notifications (
            client_id, notification_type, title, message, related_id,
            priority, action_url, action_label
        ) VALUES (
            NEW.client_id,
            'shift_completed',
            CONCAT('اكتمل الشيفت: ', NEW.shift_name),
            CONCAT('تم اكتمال الشيفت "', NEW.shift_name, '" بتاريخ ', NEW.shift_date, '. يمكنك الآن مراجعة التقرير وإنشاء الملخص.'),
            NEW.shift_id,
            'medium',
            CONCAT('shift_summaries.php?shift_id=', NEW.shift_id),
            'عرض التفاصيل'
        );
    END IF;
END //
DELIMITER ;

-- 12. إنشاء trigger لإشعار عند إنشاء ملخص الشيفت
DELIMITER //
CREATE OR REPLACE TRIGGER shift_summary_notification
AFTER INSERT ON shift_summaries
FOR EACH ROW
BEGIN
    DECLARE shift_name VARCHAR(100);
    DECLARE shift_date DATE;
    DECLARE client_id INT;

    -- جلب معلومات الشيفت
    SELECT s.shift_name, s.shift_date, s.client_id
    INTO shift_name, shift_date, client_id
    FROM shifts s WHERE s.shift_id = NEW.shift_id;

    -- إنشاء إشعار
    INSERT INTO admin_notifications (
        client_id, notification_type, title, message, related_id,
        priority, action_url, action_label
    ) VALUES (
        client_id,
        'shift_summary_ready',
        CONCAT('تقرير الشيفت جاهز: ', shift_name),
        CONCAT('تم إنشاء تقرير شامل للشيفت "', shift_name, '" بتاريخ ', shift_date, '. نقاط الأداء: ', NEW.performance_score, '/10'),
        NEW.summary_id,
        CASE
            WHEN NEW.critical_events > 0 OR NEW.unresolved_issues > 0 THEN 'high'
            WHEN NEW.performance_score < 6.0 THEN 'medium'
            ELSE 'low'
        END,
        CONCAT('shift_summaries.php?summary_id=', NEW.summary_id),
        'عرض التقرير'
    );
END //
DELIMITER ;

-- 13. إنشاء trigger لإشعار عند الأحداث الحرجة
DELIMITER //
CREATE OR REPLACE TRIGGER critical_event_notification
AFTER INSERT ON shift_events
FOR EACH ROW
BEGIN
    DECLARE shift_name VARCHAR(100);
    DECLARE shift_date DATE;
    DECLARE client_id INT;

    -- إشعار فقط للأحداث الحرجة وعالية الأهمية
    IF NEW.severity IN ('critical', 'high') THEN
        -- جلب معلومات الشيفت
        SELECT s.shift_name, s.shift_date, s.client_id
        INTO shift_name, shift_date, client_id
        FROM shifts s WHERE s.shift_id = NEW.shift_id;

        -- إنشاء إشعار
        INSERT INTO admin_notifications (
            client_id, notification_type, title, message, related_id,
            priority, action_url, action_label
        ) VALUES (
            client_id,
            'critical_event',
            CONCAT('حدث ', CASE WHEN NEW.severity = 'critical' THEN 'حرج' ELSE 'مهم' END, ': ', NEW.event_title),
            CONCAT('حدث ', CASE WHEN NEW.severity = 'critical' THEN 'حرج' ELSE 'مهم' END, ' في الشيفت "', shift_name, '" - ', NEW.event_description),
            NEW.event_id,
            CASE WHEN NEW.severity = 'critical' THEN 'urgent' ELSE 'high' END,
            CONCAT('shift_events.php?event_id=', NEW.event_id),
            'عرض الحدث'
        );
    END IF;
END //
DELIMITER ;

-- 14. إضافة صفحات جديدة للنظام
INSERT IGNORE INTO pages (page_name, page_label, page_url, page_icon, category) VALUES
('shift_events', 'أحداث الشيفت', 'shift_events.php', 'fas fa-list-alt', 'shifts'),
('shift_summaries', 'ملخصات الشيفت', 'shift_summaries.php', 'fas fa-chart-pie', 'shifts'),
('shift_tasks', 'مهام الشيفت', 'shift_tasks.php', 'fas fa-tasks', 'shifts'),
('notifications', 'الإشعارات', 'notifications.php', 'fas fa-bell', 'system');
