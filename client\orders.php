<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة الأوردرات
    if (!hasPagePermission('orders')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة الأوردرات';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('orders')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_orders') && !employeeHasPermission('view_orders')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "إدارة الأوردرات";
$active_page = "orders";

// التحقق من وجود جدول orders وإنشاؤه إذا لم يكن موجوداً
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            customer_id INT DEFAULT NULL,
            session_id INT DEFAULT NULL,
            order_number VARCHAR(50) NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            status ENUM('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
            payment_method ENUM('cash','card','other') DEFAULT 'cash',
            notes TEXT DEFAULT NULL,
            created_by INT DEFAULT NULL,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            tax_amount DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_orders_client_id (client_id),
            INDEX idx_orders_customer_id (customer_id),
            INDEX idx_orders_status (status),
            INDEX idx_orders_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_order_items_order_id (order_id),
            INDEX idx_order_items_product_id (product_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (PDOException $e) {
    // تجاهل الأخطاء في حالة وجود الجداول مسبقاً
}

// جلب إحصائيات سريعة
try {
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
            COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount END), 0) as total_revenue
        FROM orders 
        WHERE client_id = ? AND DATE(created_at) = CURDATE()
    ");
    $stats_stmt->execute([$client_id]);
    $today_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $today_stats = [
        'total_orders' => 0,
        'pending_orders' => 0,
        'completed_orders' => 0,
        'cancelled_orders' => 0,
        'total_revenue' => 0
    ];
}

require_once 'includes/header.php';
?>

<div class="container-fluid"><br>
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shopping-cart me-2"></i>
            إدارة الأوردرات
        </h1>
        <div>
            <button type="button" class="btn btn-outline-secondary me-2" onclick="reloadAllData()" title="إعادة تحميل البيانات">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث
            </button>
            <button type="button" class="btn btn-primary" onclick="showCreateOrderModal()">
                <i class="fas fa-plus me-2"></i>
                أوردر جديد
            </button>
        </div>
    </div>

    <!-- إحصائيات اليوم -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الأوردرات اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($today_stats['total_orders']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                قيد الانتظار
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($today_stats['pending_orders']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                مكتملة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($today_stats['completed_orders']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إيرادات اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($today_stats['total_revenue'], 2); ?> ج.م
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">البحث والتصفية</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="رقم الأوردر، العميل، الملاحظات...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="pending">قيد الانتظار</option>
                        <option value="completed">مكتمل</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="dateFrom">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="dateTo">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="button" class="btn btn-primary me-2" onclick="loadOrders()">
                        <i class="fas fa-search me-1"></i>بحث
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>مسح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأوردرات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الأوردرات</h6>
        </div>
        <div class="card-body">
            <div id="ordersContainer">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل الأوردرات...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء أوردر جديد -->
<div class="modal fade" id="createOrderModal" tabindex="-1" aria-labelledby="createOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createOrderModalLabel">
                    <i class="fas fa-plus me-2"></i>إنشاء أوردر جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createOrderForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">العميل (اختياري)</label>
                            <select class="form-select" id="orderCustomerId">
                                <option value="">بدون عميل</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="orderPaymentMethod" required>
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الملاحظات</label>
                        <textarea class="form-control" id="orderNotes" rows="2" placeholder="ملاحظات إضافية..."></textarea>
                    </div>

                    <hr>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">المنتجات</h6>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="showAddProductModal()">
                            <i class="fas fa-plus me-1"></i>إضافة منتج
                        </button>
                    </div>

                    <div id="orderItemsList">
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-shopping-basket fa-2x mb-2"></i>
                            <p>لم يتم إضافة منتجات بعد</p>
                        </div>
                    </div>

                    <div class="border-top pt-3 mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>إجمالي المنتجات: <span id="totalItems">0</span></strong>
                            </div>
                            <div class="col-md-6 text-end">
                                <strong>المجموع الكلي: <span id="totalAmount">0.00</span> ج.م </strong>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="createOrder()" id="createOrderBtn">
                    <i class="fas fa-save me-2"></i>إنشاء الأوردر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة منتج -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">
                    <i class="fas fa-plus me-2"></i>إضافة منتج
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">البحث عن منتج</label>
                        <input type="text" class="form-control" id="productSearch" placeholder="اسم المنتج...">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">التصنيف</label>
                        <select class="form-select" id="categoryFilter">
                            <option value="">جميع التصنيفات</option>
                        </select>
                    </div>
                </div>

                <div id="productsList" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل الأوردر -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderDetailsModalLabel">
                    <i class="fas fa-eye me-2"></i>تفاصيل الأوردر
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// متغيرات عامة
let currentPage = 1;
let selectedOrderItems = [];
let allProducts = [];
let allCustomers = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل صفحة الأوردرات...');
    loadOrders();
    loadCustomers();
    loadProducts();
});

// دالة إعادة تحميل جميع البيانات
function reloadAllData() {
    console.log('إعادة تحميل جميع البيانات...');
    loadOrders(currentPage);
    loadCustomers();
    loadProducts();
}

// تحميل الأوردرات
function loadOrders(page = 1) {
    currentPage = page;

    const search = document.getElementById('searchInput').value;
    const status = document.getElementById('statusFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;

    const params = new URLSearchParams({
        page: page,
        limit: 20
    });

    if (search) params.append('search', search);
    if (status) params.append('status', status);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    fetch(`api/get_orders.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOrders(data.orders, data.pagination);
            } else {
                showError('حدث خطأ أثناء تحميل الأوردرات: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ في الاتصال');
        });
}

// عرض الأوردرات
function displayOrders(orders, pagination) {
    const container = document.getElementById('ordersContainer');

    if (orders.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أوردرات</h5>
                <p class="text-muted">لم يتم العثور على أوردرات تطابق معايير البحث</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>رقم الأوردر</th>
                        <th>العميل</th>
                        <th>عدد المنتجات</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                        <th>طريقة الدفع</th>
                        <th>التاريخ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    orders.forEach((order, index) => {
        const statusClass = {
            'pending': 'warning',
            'completed': 'success',
            'cancelled': 'danger'
        }[order.status] || 'secondary';

        html += `
            <tr>
                <td>${(pagination.current_page - 1) * pagination.per_page + index + 1}</td>
                <td><strong>${order.order_number}</strong></td>
                <td>${order.customer_name || 'غير محدد'}</td>
                <td><span class="badge bg-info">${order.items_count}</span></td>
                <td><strong>${order.total_amount_formatted} ج.م</strong></td>
                <td><span class="badge bg-${statusClass}">${order.status_text}</span></td>
                <td>${order.payment_method_text}</td>
                <td>${order.created_at_formatted}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewOrderDetails(${order.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="updateOrderStatus(${order.id}, 'completed')" title="تأكيد الأوردر" ${order.status === 'completed' ? 'disabled' : ''}>
                            <i class="fas fa-check"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="updateOrderStatus(${order.id}, 'cancelled')" title="إلغاء الأوردر" ${order.status === 'cancelled' ? 'disabled' : ''}>
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    // إضافة pagination
    if (pagination.total_pages > 1) {
        html += `
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
        `;

        // Previous button
        html += `
            <li class="page-item ${pagination.current_page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadOrders(${pagination.current_page - 1})" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= pagination.total_pages; i++) {
            if (i === pagination.current_page ||
                i === 1 ||
                i === pagination.total_pages ||
                (i >= pagination.current_page - 2 && i <= pagination.current_page + 2)) {
                html += `
                    <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadOrders(${i})">${i}</a>
                    </li>
                `;
            } else if (i === pagination.current_page - 3 || i === pagination.current_page + 3) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        // Next button
        html += `
            <li class="page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadOrders(${pagination.current_page + 1})" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `;

        html += `
                </ul>
            </nav>
        `;
    }

    container.innerHTML = html;
}

// تحميل العملاء
function loadCustomers() {
    // جلب جميع العملاء للعميل الحالي
    fetch('api/get_all_customers.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allCustomers = data.customers;
                const select = document.getElementById('orderCustomerId');
                select.innerHTML = '<option value="">بدون عميل</option>';

                data.customers.forEach(customer => {
                    const customerId = customer.customer_id || customer.id;
                    select.innerHTML += `<option value="${customerId}">${customer.name} - ${customer.phone}</option>`;
                });

                console.log(`تم تحميل ${data.customers.length} عميل`);
            } else {
                console.error('خطأ في تحميل العملاء:', data.error);
            }
        })
        .catch(error => {
            console.error('Error loading customers:', error);
            // في حالة الخطأ، جرب API البحث
            loadCustomersFromSearch();
        });
}

// تحميل العملاء من API البحث (كبديل)
function loadCustomersFromSearch() {
    fetch('api/search-customers.php?q=')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.customers) {
                allCustomers = data.customers;
                const select = document.getElementById('orderCustomerId');
                select.innerHTML = '<option value="">بدون عميل</option>';

                data.customers.forEach(customer => {
                    const customerId = customer.customer_id || customer.id;
                    select.innerHTML += `<option value="${customerId}">${customer.name} - ${customer.phone}</option>`;
                });
            }
        })
        .catch(error => console.error('Error loading customers from search:', error));
}

// تحميل المنتجات
function loadProducts() {
    fetch('api/get_products.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allProducts = data.products;
                displayProducts(data.products);

                // تحديث قائمة التصنيفات
                const categories = [...new Set(data.products.map(p => p.category))];
                const categorySelect = document.getElementById('categoryFilter');
                categorySelect.innerHTML = '<option value="">جميع التصنيفات</option>';
                categories.forEach(category => {
                    categorySelect.innerHTML += `<option value="${category}">${category}</option>`;
                });
            }
        })
        .catch(error => console.error('Error loading products:', error));
}

// عرض المنتجات في modal إضافة منتج
function displayProducts(products) {
    const container = document.getElementById('productsList');

    if (!products || products.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-box-open fa-2x text-muted mb-2"></i>
                <p class="text-muted">لا توجد منتجات</p>
                <button type="button" class="btn btn-outline-primary" onclick="loadProducts()">
                    <i class="fas fa-refresh me-1"></i>إعادة تحميل
                </button>
            </div>
        `;
        return;
    }

    let html = '<div class="row">';

    products.forEach(product => {
        // التأكد من وجود البيانات المطلوبة
        const productId = product.id;
        const productName = product.name || 'منتج غير محدد';
        const productCategory = product.category || 'غير مصنف';
        const productPrice = parseFloat(product.price) || 0;
        const productDescription = product.description || '';

        // تنظيف اسم المنتج للاستخدام في JavaScript
        const cleanProductName = productName.replace(/'/g, "\\'").replace(/"/g, '\\"');

        html += `
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">${productName}</h6>
                        <p class="card-text">
                            <small class="text-muted">${productCategory}</small><br>
                            <strong>${productPrice.toFixed(2)} ج.م</strong>
                        </p>
                        ${productDescription ? `<p class="text-muted small">${productDescription}</p>` : ''}
                        <div class="d-flex align-items-center">
                            <input type="number" class="form-control form-control-sm me-2"
                                   id="qty_${productId}" value="1" min="1" max="99" style="width: 80px;">
                            <button type="button" class="btn btn-sm btn-primary"
                                    onclick="addProductToOrder(${productId}, '${cleanProductName}', ${productPrice})">
                                <i class="fas fa-plus me-1"></i>إضافة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;

    console.log(`تم عرض ${products.length} منتج`);
}

// إضافة منتج للأوردر
function addProductToOrder(productId, productName, productPrice) {
    const qtyInput = document.getElementById(`qty_${productId}`);
    if (!qtyInput) {
        showError('لم يتم العثور على حقل الكمية');
        return;
    }

    const quantity = parseInt(qtyInput.value) || 1;

    if (quantity <= 0) {
        showError('الكمية يجب أن تكون أكبر من صفر');
        return;
    }

    // البحث عن المنتج في القائمة المختارة
    const existingIndex = selectedOrderItems.findIndex(item => item.product_id === productId);

    if (existingIndex >= 0) {
        // إذا كان المنتج موجود، زيادة الكمية
        selectedOrderItems[existingIndex].quantity += quantity;
    } else {
        // إضافة منتج جديد
        selectedOrderItems.push({
            product_id: productId,
            name: productName,
            price: parseFloat(productPrice) || 0,
            quantity: quantity
        });
    }

    // تحديث عرض المنتجات المختارة
    updateOrderItemsDisplay();

    // إعادة تعيين الكمية
    qtyInput.value = 1;

    // إغلاق modal إضافة المنتج
    const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
    if (modal) {
        modal.hide();
    }

    showSuccess(`تم إضافة ${quantity} ${productName} للأوردر`);
}

// تحديث عرض المنتجات المختارة
function updateOrderItemsDisplay() {
    const container = document.getElementById('orderItemsList');

    if (selectedOrderItems.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-shopping-basket fa-2x mb-2"></i>
                <p>لم يتم إضافة منتجات بعد</p>
            </div>
        `;
        document.getElementById('totalItems').textContent = '0';
        document.getElementById('totalAmount').textContent = '0.00';
        return;
    }

    let html = '<div class="list-group">';
    let totalAmount = 0;
    let totalItems = 0;

    selectedOrderItems.forEach((item, index) => {
        const itemTotal = item.price * item.quantity;
        totalAmount += itemTotal;
        totalItems += item.quantity;

        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${item.name}</h6>
                    <small class="text-muted">${item.price.toFixed(2)} ج.م × ${item.quantity}</small>
                </div>
                <div class="d-flex align-items-center">
                    <strong class="me-3">${itemTotal.toFixed(2)} ج.م</strong>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOrderItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;

    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);
}

// حذف منتج من الأوردر
function removeOrderItem(index) {
    selectedOrderItems.splice(index, 1);
    updateOrderItemsDisplay();
}

// إظهار modal إنشاء أوردر جديد
function showCreateOrderModal() {
    selectedOrderItems = [];
    updateOrderItemsDisplay();

    // إعادة تعيين النموذج
    document.getElementById('createOrderForm').reset();

    const modal = new bootstrap.Modal(document.getElementById('createOrderModal'));
    modal.show();
}

// إظهار modal إضافة منتج
function showAddProductModal() {
    const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
    modal.show();

    // تطبيق فلاتر البحث
    applyProductFilters();
}

// تطبيق فلاتر البحث على المنتجات
function applyProductFilters() {
    const search = document.getElementById('productSearch').value.toLowerCase();
    const category = document.getElementById('categoryFilter').value;

    let filteredProducts = allProducts;

    if (search) {
        filteredProducts = filteredProducts.filter(product =>
            product.name.toLowerCase().includes(search)
        );
    }

    if (category) {
        filteredProducts = filteredProducts.filter(product =>
            product.category === category
        );
    }

    displayProducts(filteredProducts);
}

// إنشاء الأوردر
function createOrder() {
    if (selectedOrderItems.length === 0) {
        showError('يجب إضافة منتج واحد على الأقل');
        return;
    }

    const customerId = document.getElementById('orderCustomerId').value;
    const paymentMethod = document.getElementById('orderPaymentMethod').value;
    const notes = document.getElementById('orderNotes').value;

    const orderData = {
        customer_id: customerId || null,
        payment_method: paymentMethod,
        notes: notes,
        items: selectedOrderItems
    };

    const createBtn = document.getElementById('createOrderBtn');
    const originalText = createBtn.innerHTML;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
    createBtn.disabled = true;

    fetch('api/create_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(`تم إنشاء الأوردر بنجاح - رقم الأوردر: ${data.order_number}`);

            // إغلاق modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('createOrderModal'));
            modal.hide();

            // إعادة تحميل الأوردرات
            loadOrders();
        } else {
            showError('حدث خطأ أثناء إنشاء الأوردر: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ في الاتصال');
    })
    .finally(() => {
        createBtn.innerHTML = originalText;
        createBtn.disabled = false;
    });
}

// تحديث حالة الأوردر
function updateOrderStatus(orderId, status) {
    const statusText = {
        'completed': 'تأكيد',
        'cancelled': 'إلغاء'
    }[status];

    Swal.fire({
        title: `${statusText} الأوردر`,
        text: `هل أنت متأكد من ${statusText.toLowerCase()} هذا الأوردر؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: status === 'completed' ? '#28a745' : '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: `نعم، ${statusText}`,
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('api/update_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    order_id: orderId,
                    action: 'update_status',
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadOrders(currentPage);
                } else {
                    showError('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('حدث خطأ في الاتصال');
            });
        }
    });
}

// عرض تفاصيل الأوردر
function viewOrderDetails(orderId) {
    fetch(`api/get_order_details.php?order_id=${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOrderDetails(data.order, data.items, data.statistics);

                const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
                modal.show();
            } else {
                showError('حدث خطأ أثناء جلب تفاصيل الأوردر: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ في الاتصال');
        });
}

// عرض تفاصيل الأوردر في modal
function displayOrderDetails(order, items, statistics) {
    const statusClass = {
        'pending': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
    }[order.status] || 'secondary';

    let html = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h6>معلومات الأوردر</h6>
                <table class="table table-sm">
                    <tr><td><strong>رقم الأوردر:</strong></td><td>${order.order_number}</td></tr>
                    <tr><td><strong>العميل:</strong></td><td>${order.customer_name || 'غير محدد'}</td></tr>
                    <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-${statusClass}">${order.status_text}</span></td></tr>
                    <tr><td><strong>طريقة الدفع:</strong></td><td>${order.payment_method_text}</td></tr>
                    <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${order.created_at_formatted}</td></tr>
                    <tr><td><strong>آخر تحديث:</strong></td><td>${order.updated_at_formatted}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>الإحصائيات</h6>
                <table class="table table-sm">
                    <tr><td><strong>عدد المنتجات:</strong></td><td>${statistics.total_items}</td></tr>
                    <tr><td><strong>إجمالي الكمية:</strong></td><td>${statistics.total_quantity}</td></tr>
                    <tr><td><strong>المبلغ الكلي:</strong></td><td><strong>${order.total_amount_formatted} ج.م</strong></td></tr>
                </table>
                ${order.notes ? `<div class="mt-3"><h6>الملاحظات</h6><p class="text-muted">${order.notes}</p></div>` : ''}
            </div>
        </div>

        <h6>المنتجات</h6>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المنتج</th>
                        <th>التصنيف</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
    `;

    items.forEach((item, index) => {
        html += `
            <tr>
                <td>${index + 1}</td>
                <td>${item.product_name}</td>
                <td><span class="badge bg-secondary">${item.product_category}</span></td>
                <td>${item.price_formatted} ج.م</td>
                <td>${item.quantity}</td>
                <td><strong>${item.total_price_formatted} ج.م</strong></td>
            </tr>
        `;
    });

    html += `
                </tbody>
                <tfoot>
                    <tr class="table-active">
                        <td colspan="5"><strong>المجموع الكلي</strong></td>
                        <td><strong>${order.total_amount_formatted}ج.م</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    document.getElementById('orderDetailsContent').innerHTML = html;
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFrom').value = '';
    document.getElementById('dateTo').value = '';
    loadOrders();
}

// دوال المساعدة
function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: 'نجح!',
        text: message,
        showConfirmButton: false,
        timer: 2000
    });
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'خطأ!',
        text: message
    });
}

// Event listeners
document.getElementById('productSearch').addEventListener('input', applyProductFilters);
document.getElementById('categoryFilter').addEventListener('change', applyProductFilters);

// البحث عند الضغط على Enter
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        loadOrders();
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
