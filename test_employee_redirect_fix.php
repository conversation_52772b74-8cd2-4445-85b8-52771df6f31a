<?php
/**
 * اختبار إصلاح مشكلة إعادة توجيه الموظفين
 * هذا الملف يختبر أن الموظفين يتم توجيههم إلى صفحة تسجيل الدخول الصحيحة
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار إصلاح توجيه الموظفين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        .test-card { margin: 20px 0; }
        .test-success { color: #28a745; }
        .test-error { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-info { color: #17a2b8; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card test-card'>
                <div class='card-header bg-primary text-white'>
                    <h4><i class='fas fa-bug-slash me-2'></i>اختبار إصلاح مشكلة توجيه الموظفين</h4>
                    <p class='mb-0'>فحص الملفات التي تم إصلاحها لضمان التوجيه الصحيح للموظفين</p>
                </div>
                <div class='card-body'>";

// قائمة الملفات التي تم إصلاحها
$fixed_files = [
    'client/dashboard.php' => 'لوحة التحكم الرئيسية',
    'client/employees.php' => 'إدارة الموظفين',
    'client/cafeteria.php' => 'إدارة الكافتيريا',
    'client/customer_details.php' => 'تفاصيل العميل',
    'client/sessions.php' => 'إدارة الجلسات',
    'client/quick_attendance.php' => 'تسجيل الحضور السريع',
    'client/orders.php' => 'إدارة الطلبات',
    'client/change_username.php' => 'تغيير اسم المستخدم',
    'client/delete_customer.php' => 'حذف العميل',
    'client/rooms.php' => 'إدارة الغرف',
    'client/customers.php' => 'إدارة العملاء',
    'client/edit_customer.php' => 'تعديل بيانات العميل',
    'client/includes/auth.php' => 'ملف المصادقة',
    'client/test_dropdown.php' => 'اختبار القائمة المنسدلة',
    'client/profile.php' => 'الملف الشخصي'
];

echo "<h5><i class='fas fa-file-check me-2'></i>الملفات التي تم إصلاحها:</h5>";
echo "<div class='row'>";

$success_count = 0;
$total_count = count($fixed_files);

foreach ($fixed_files as $file => $description) {
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='card border-success'>";
    echo "<div class='card-body'>";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // فحص وجود التوجيه المحسن
        if (strpos($content, 'employee-login.php') !== false && 
            strpos($content, 'current_url') !== false) {
            echo "<h6 class='test-success'><i class='fas fa-check-circle me-2'></i>$description</h6>";
            echo "<small class='text-muted'>$file</small><br>";
            echo "<span class='badge bg-success'>تم الإصلاح ✓</span>";
            $success_count++;
        } else if (strpos($content, 'employee-login.php') !== false) {
            echo "<h6 class='test-warning'><i class='fas fa-exclamation-triangle me-2'></i>$description</h6>";
            echo "<small class='text-muted'>$file</small><br>";
            echo "<span class='badge bg-warning'>إصلاح جزئي</span>";
            $success_count++;
        } else {
            echo "<h6 class='test-error'><i class='fas fa-times-circle me-2'></i>$description</h6>";
            echo "<small class='text-muted'>$file</small><br>";
            echo "<span class='badge bg-danger'>يحتاج إصلاح</span>";
        }
    } else {
        echo "<h6 class='test-error'><i class='fas fa-file-times me-2'></i>$description</h6>";
        echo "<small class='text-muted'>$file</small><br>";
        echo "<span class='badge bg-secondary'>الملف غير موجود</span>";
    }
    
    echo "</div></div></div>";
}

echo "</div>";

// إحصائيات الإصلاح
echo "<div class='alert alert-info mt-4'>";
echo "<h5><i class='fas fa-chart-pie me-2'></i>إحصائيات الإصلاح</h5>";
echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<div class='text-center'>";
echo "<h3 class='test-success'>$success_count</h3>";
echo "<p>ملف تم إصلاحه</p>";
echo "</div></div>";
echo "<div class='col-md-4'>";
echo "<div class='text-center'>";
echo "<h3 class='test-info'>$total_count</h3>";
echo "<p>إجمالي الملفات</p>";
echo "</div></div>";
echo "<div class='col-md-4'>";
echo "<div class='text-center'>";
$percentage = round(($success_count / $total_count) * 100);
echo "<h3 class='test-warning'>$percentage%</h3>";
echo "<p>نسبة الإصلاح</p>";
echo "</div></div>";
echo "</div></div>";

// فحص ملف auth_guard.php
echo "<h5 class='mt-4'><i class='fas fa-shield-alt me-2'></i>فحص ملف الحماية الرئيسي:</h5>";
if (file_exists('includes/auth_guard.php')) {
    $auth_content = file_get_contents('includes/auth_guard.php');
    if (strpos($auth_content, 'protectEmployeePage') !== false && 
        strpos($auth_content, 'employee-login.php') !== false) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "ملف auth_guard.php يحتوي على التوجيه الصحيح للموظفين";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "ملف auth_guard.php يحتاج إلى مراجعة";
        echo "</div>";
    }
} else {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-file-times me-2'></i>";
    echo "ملف auth_guard.php غير موجود";
    echo "</div>";
}

// تعليمات الاختبار
echo "<div class='alert alert-primary mt-4'>";
echo "<h5><i class='fas fa-info-circle me-2'></i>كيفية اختبار الإصلاح:</h5>";
echo "<ol>";
echo "<li>قم بتسجيل الخروج من أي جلسة حالية</li>";
echo "<li>حاول الوصول إلى أي صفحة من صفحات الموظفين مباشرة</li>";
echo "<li>يجب أن يتم توجيهك إلى صفحة تسجيل دخول الموظف (employee-login.php)</li>";
echo "<li>سجل دخول كموظف وتأكد من عمل النظام بشكل صحيح</li>";
echo "</ol>";
echo "</div>";

// روابط مفيدة
echo "<div class='card mt-4'>";
echo "<div class='card-header'>";
echo "<h5><i class='fas fa-link me-2'></i>روابط مفيدة للاختبار</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>صفحات تسجيل الدخول:</h6>";
echo "<a href='client/login.php' class='btn btn-outline-primary btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-user me-1'></i>دخول العميل";
echo "</a>";
echo "<a href='client/employee-login.php' class='btn btn-outline-success btn-sm mb-2' target='_blank'>";
echo "<i class='fas fa-user-tie me-1'></i>دخول الموظف";
echo "</a>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6>صفحات للاختبار:</h6>";
echo "<a href='client/dashboard.php' class='btn btn-outline-info btn-sm me-2 mb-2' target='_blank'>";
echo "<i class='fas fa-tachometer-alt me-1'></i>لوحة التحكم";
echo "</a>";
echo "<a href='client/employees.php' class='btn btn-outline-warning btn-sm mb-2' target='_blank'>";
echo "<i class='fas fa-users me-1'></i>إدارة الموظفين";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "            </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
