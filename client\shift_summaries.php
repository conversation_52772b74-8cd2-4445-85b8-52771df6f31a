<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id'])) {
    if (!employeeCanAccessPage('shift_summaries')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    if (!employeeHasPermission('view_shift_summaries') && !employeeHasPermission('generate_shift_reports')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$current_employee_id = $_SESSION['employee_id'] ?? null;

$page_title = "ملخصات الشيفت";
$active_page = "shift_summaries";

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'generate_summary':
                    // استدعاء الإجراء المخزن لإنشاء الملخص
                    $stmt = $pdo->prepare("CALL GenerateShiftSummary(?)");
                    $stmt->execute([$_POST['shift_id']]);
                    
                    $_SESSION['success'] = "تم إنشاء ملخص الشيفت بنجاح";
                    break;

                case 'update_notes':
                    $stmt = $pdo->prepare("
                        UPDATE shift_summaries 
                        SET shift_notes = ?, generated_at = CURRENT_TIMESTAMP, generated_by = ?
                        WHERE summary_id = ?
                    ");
                    $stmt->execute([
                        $_POST['shift_notes'],
                        $current_employee_id ?: $client_id,
                        $_POST['summary_id']
                    ]);
                    
                    $_SESSION['success'] = "تم تحديث ملاحظات الشيفت بنجاح";
                    break;

                case 'delete_summary':
                    $stmt = $pdo->prepare("DELETE FROM shift_summaries WHERE summary_id = ?");
                    $stmt->execute([$_POST['summary_id']]);
                    
                    $_SESSION['success'] = "تم حذف ملخص الشيفت بنجاح";
                    break;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
    
    header('Location: shift_summaries.php');
    exit;
}

// الفلاتر
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // بداية الشهر الحالي
$date_to = $_GET['date_to'] ?? date('Y-m-t'); // نهاية الشهر الحالي
$status_filter = $_GET['status_filter'] ?? '';
$performance_filter = $_GET['performance_filter'] ?? '';

// بناء الاستعلام
$where_conditions = ["s.client_id = ?"];
$params = [$client_id];

if ($date_from && $date_to) {
    $where_conditions[] = "s.shift_date BETWEEN ? AND ?";
    $params[] = $date_from;
    $params[] = $date_to;
}

if ($status_filter) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
}

if ($performance_filter) {
    switch ($performance_filter) {
        case 'excellent':
            $where_conditions[] = "ss.performance_score >= 8.0";
            break;
        case 'good':
            $where_conditions[] = "ss.performance_score >= 6.0 AND ss.performance_score < 8.0";
            break;
        case 'poor':
            $where_conditions[] = "ss.performance_score < 6.0";
            break;
    }
}

$where_clause = implode(' AND ', $where_conditions);

// جلب ملخصات الشيفت
$summaries_query = "
    SELECT s.*, ss.*,
           supervisor.name as supervisor_name,
           generator.name as generated_by_name,
           CASE 
               WHEN ss.performance_score >= 8.0 THEN 'ممتاز'
               WHEN ss.performance_score >= 6.0 THEN 'جيد'
               WHEN ss.performance_score >= 4.0 THEN 'مقبول'
               ELSE 'ضعيف'
           END as performance_grade
    FROM shifts s
    LEFT JOIN shift_summaries ss ON s.shift_id = ss.shift_id
    LEFT JOIN employees supervisor ON s.shift_supervisor = supervisor.id
    LEFT JOIN employees generator ON ss.generated_by = generator.id
    WHERE $where_clause
    ORDER BY s.shift_date DESC, s.start_time DESC
    LIMIT 50
";

$summaries = $pdo->prepare($summaries_query);
$summaries->execute($params);
$summaries_data = $summaries->fetchAll(PDO::FETCH_ASSOC);

// إحصائيات عامة
$stats_query = "
    SELECT 
        COUNT(DISTINCT s.shift_id) as total_shifts,
        COUNT(ss.summary_id) as summarized_shifts,
        AVG(ss.performance_score) as avg_performance,
        AVG(ss.attendance_percentage) as avg_attendance,
        SUM(ss.total_work_hours) as total_work_hours,
        SUM(ss.critical_events) as total_critical_events,
        SUM(ss.unresolved_issues) as total_unresolved_issues
    FROM shifts s
    LEFT JOIN shift_summaries ss ON s.shift_id = ss.shift_id
    WHERE s.client_id = ? AND s.shift_date BETWEEN ? AND ?
";

$stats = $pdo->prepare($stats_query);
$stats->execute([$client_id, $date_from, $date_to]);
$stats_data = $stats->fetch(PDO::FETCH_ASSOC);

// جلب الشيفتات التي لم يتم إنشاء ملخص لها
$pending_shifts_query = "
    SELECT s.shift_id, s.shift_name, s.shift_date, s.status
    FROM shifts s
    LEFT JOIN shift_summaries ss ON s.shift_id = ss.shift_id
    WHERE s.client_id = ? AND s.status = 'completed' 
    AND ss.summary_id IS NULL
    ORDER BY s.shift_date DESC
    LIMIT 10
";

$pending_shifts = $pdo->prepare($pending_shifts_query);
$pending_shifts->execute([$client_id]);
$pending_shifts_data = $pending_shifts->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-chart-pie me-2"></i><?php echo $page_title; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshPage()">
                        <i class="fas fa-sync-alt me-1"></i>تحديث
                    </button>
                </div>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-white bg-primary">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['total_shifts']; ?></h4>
                            <p class="card-text">إجمالي الشيفتات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-success">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['summarized_shifts']; ?></h4>
                            <p class="card-text">ملخصة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-info">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo number_format($stats_data['avg_performance'] ?? 0, 1); ?></h4>
                            <p class="card-text">متوسط الأداء</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-warning">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo number_format($stats_data['avg_attendance'] ?? 0, 1); ?>%</h4>
                            <p class="card-text">متوسط الحضور</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-secondary">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo number_format($stats_data['total_work_hours'] ?? 0, 0); ?></h4>
                            <p class="card-text">ساعات العمل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-white bg-danger">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['total_critical_events'] ?? 0; ?></h4>
                            <p class="card-text">أحداث حرجة</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- الشيفتات المعلقة -->
                <?php if (!empty($pending_shifts_data)): ?>
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>شيفتات بحاجة لملخص
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($pending_shifts_data as $shift): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                    <div>
                                        <strong><?php echo htmlspecialchars($shift['shift_name']); ?></strong><br>
                                        <small class="text-muted"><?php echo $shift['shift_date']; ?></small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-primary" onclick="generateSummary(<?php echo $shift['shift_id']; ?>)">
                                        <i class="fas fa-plus"></i> إنشاء
                                    </button>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- فلاتر البحث -->
                <div class="col-md-<?php echo !empty($pending_shifts_data) ? '8' : '12'; ?>">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">فلاتر البحث</h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="status_filter" class="form-label">حالة الشيفت</label>
                                    <select class="form-select" id="status_filter" name="status_filter">
                                        <option value="">جميع الحالات</option>
                                        <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                        <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                                        <option value="cancelled" <?php echo $status_filter == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="performance_filter" class="form-label">مستوى الأداء</label>
                                    <select class="form-select" id="performance_filter" name="performance_filter">
                                        <option value="">جميع المستويات</option>
                                        <option value="excellent" <?php echo $performance_filter == 'excellent' ? 'selected' : ''; ?>>ممتاز (8+)</option>
                                        <option value="good" <?php echo $performance_filter == 'good' ? 'selected' : ''; ?>>جيد (6-8)</option>
                                        <option value="poor" <?php echo $performance_filter == 'poor' ? 'selected' : ''; ?>>ضعيف (&lt;6)</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>بحث
                                    </button>
                                    <a href="shift_summaries.php" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الملخصات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">ملخصات الشيفتات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($summaries_data)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> لا توجد ملخصات للفترة المحددة
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الشيفت</th>
                                        <th>التاريخ</th>
                                        <th>الحضور</th>
                                        <th>ساعات العمل</th>
                                        <th>الأداء</th>
                                        <th>الأحداث</th>
                                        <th>المشاكل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($summaries_data as $summary): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($summary['shift_name']); ?></strong>
                                            <?php if ($summary['supervisor_name']): ?>
                                                <br><small class="text-muted">مشرف: <?php echo htmlspecialchars($summary['supervisor_name']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $summary['shift_date']; ?><br>
                                            <small class="text-muted">
                                                <?php echo date('H:i', strtotime($summary['start_time'])); ?> - 
                                                <?php echo date('H:i', strtotime($summary['end_time'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($summary['summary_id']): ?>
                                                <strong><?php echo $summary['total_employees_attended']; ?>/<?php echo $summary['total_employees_assigned']; ?></strong><br>
                                                <small class="text-muted"><?php echo number_format($summary['attendance_percentage'], 1); ?>%</small>
                                            <?php else: ?>
                                                <span class="text-muted">لا يوجد ملخص</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($summary['summary_id']): ?>
                                                <strong><?php echo number_format($summary['total_work_hours'], 1); ?></strong> ساعة
                                                <?php if ($summary['total_overtime_hours'] > 0): ?>
                                                    <br><small class="text-info">إضافي: <?php echo number_format($summary['total_overtime_hours'], 1); ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($summary['summary_id'] && $summary['performance_score']): ?>
                                                <?php
                                                $score = $summary['performance_score'];
                                                $grade_class = $score >= 8 ? 'success' : ($score >= 6 ? 'warning' : 'danger');
                                                ?>
                                                <span class="badge bg-<?php echo $grade_class; ?>">
                                                    <?php echo number_format($score, 1); ?>/10
                                                </span><br>
                                                <small><?php echo $summary['performance_grade']; ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">غير مقيم</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($summary['summary_id']): ?>
                                                <span class="badge bg-primary"><?php echo $summary['total_events']; ?></span>
                                                <?php if ($summary['critical_events'] > 0): ?>
                                                    <br><span class="badge bg-danger"><?php echo $summary['critical_events']; ?> حرج</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($summary['summary_id']): ?>
                                                <?php if ($summary['unresolved_issues'] > 0): ?>
                                                    <span class="badge bg-warning"><?php echo $summary['unresolved_issues']; ?> غير محلول</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">لا توجد</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group-vertical btn-group-sm" role="group">
                                                <?php if ($summary['summary_id']): ?>
                                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="viewSummary(<?php echo $summary['summary_id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editNotes(<?php echo $summary['summary_id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportSummary(<?php echo $summary['summary_id']; ?>)">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <?php if ($summary['status'] == 'completed'): ?>
                                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="generateSummary(<?php echo $summary['shift_id']; ?>)">
                                                            <i class="fas fa-plus"></i> إنشاء
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير مكتمل</span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نموذج عرض تفاصيل الملخص -->
<div class="modal fade" id="viewSummaryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل ملخص الشيفت</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="summaryDetailsContent">
                <!-- سيتم تحميل المحتوى عبر AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printSummary()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الملاحظات -->
<div class="modal fade" id="editNotesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل ملاحظات الشيفت</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editNotesForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_notes">
                    <input type="hidden" name="summary_id" id="edit_summary_id">

                    <div class="mb-3">
                        <label for="shift_notes" class="form-label">ملاحظات الشيفت</label>
                        <textarea class="form-control" id="shift_notes" name="shift_notes" rows="8" placeholder="اكتب ملاحظاتك حول الشيفت..."></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>نصائح لكتابة الملاحظات:</strong>
                        <ul class="mb-0 mt-2">
                            <li>اذكر النقاط الإيجابية في الأداء</li>
                            <li>حدد المشاكل التي واجهتها وكيف تم حلها</li>
                            <li>اقترح تحسينات للشيفتات القادمة</li>
                            <li>اذكر أي ملاحظات خاصة بالموظفين</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الملاحظات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// بيانات الملخصات للجافا سكريبت
const summariesData = <?php echo json_encode($summaries_data, JSON_UNESCAPED_UNICODE); ?>;

// تحديث الصفحة
function refreshPage() {
    location.reload();
}

// إنشاء ملخص للشيفت
function generateSummary(shiftId) {
    if (confirm('هل تريد إنشاء ملخص لهذا الشيفت؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="generate_summary">
            <input type="hidden" name="shift_id" value="${shiftId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// عرض تفاصيل الملخص
function viewSummary(summaryId) {
    // يمكن تطوير هذه الوظيفة لجلب التفاصيل عبر AJAX
    fetch(`api/get_shift_summary.php?summary_id=${summaryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySummaryDetails(data.summary);
                new bootstrap.Modal(document.getElementById('viewSummaryModal')).show();
            } else {
                alert('حدث خطأ في تحميل تفاصيل الملخص');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// عرض تفاصيل الملخص في النموذج
function displaySummaryDetails(summary) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">معلومات الشيفت</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>اسم الشيفت:</strong></td><td>${summary.shift_name}</td></tr>
                            <tr><td><strong>التاريخ:</strong></td><td>${summary.shift_date}</td></tr>
                            <tr><td><strong>الوقت:</strong></td><td>${summary.start_time} - ${summary.end_time}</td></tr>
                            <tr><td><strong>المشرف:</strong></td><td>${summary.supervisor_name || 'غير محدد'}</td></tr>
                            <tr><td><strong>الحالة:</strong></td><td>${summary.status}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">إحصائيات الحضور</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>الموظفين المخصصين:</strong></td><td>${summary.total_employees_assigned}</td></tr>
                            <tr><td><strong>الموظفين الحاضرين:</strong></td><td>${summary.total_employees_attended}</td></tr>
                            <tr><td><strong>نسبة الحضور:</strong></td><td>${summary.attendance_percentage}%</td></tr>
                            <tr><td><strong>ساعات العمل:</strong></td><td>${summary.total_work_hours} ساعة</td></tr>
                            <tr><td><strong>ساعات إضافية:</strong></td><td>${summary.total_overtime_hours} ساعة</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">الأداء</h6>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-primary">${summary.performance_score}/10</h3>
                        <p class="mb-0">${summary.performance_grade}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">الأحداث</h6>
                    </div>
                    <div class="card-body text-center">
                        <h4>${summary.total_events}</h4>
                        <small>إجمالي الأحداث</small>
                        ${summary.critical_events > 0 ? `<br><span class="badge bg-danger">${summary.critical_events} حرج</span>` : ''}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">المشاكل</h6>
                    </div>
                    <div class="card-body text-center">
                        <h4>${summary.unresolved_issues}</h4>
                        <small>غير محلولة</small>
                    </div>
                </div>
            </div>
        </div>
        ${summary.shift_notes ? `
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">ملاحظات الشيفت</h6>
                    </div>
                    <div class="card-body">
                        <p>${summary.shift_notes}</p>
                    </div>
                </div>
            </div>
        </div>
        ` : ''}
        <div class="row mt-3">
            <div class="col-12">
                <small class="text-muted">
                    تم إنشاء الملخص في: ${summary.generated_at}
                    ${summary.generated_by_name ? ` بواسطة: ${summary.generated_by_name}` : ''}
                </small>
            </div>
        </div>
    `;

    document.getElementById('summaryDetailsContent').innerHTML = content;
}

// تعديل الملاحظات
function editNotes(summaryId) {
    const summary = summariesData.find(s => s.summary_id == summaryId);
    if (!summary) return;

    document.getElementById('edit_summary_id').value = summaryId;
    document.getElementById('shift_notes').value = summary.shift_notes || '';

    new bootstrap.Modal(document.getElementById('editNotesModal')).show();
}

// تصدير الملخص
function exportSummary(summaryId) {
    // يمكن تطوير هذه الوظيفة لتصدير الملخص كـ PDF
    window.open(`api/export_shift_summary.php?summary_id=${summaryId}`, '_blank');
}

// طباعة الملخص
function printSummary() {
    const content = document.getElementById('summaryDetailsContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>ملخص الشيفت</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                @media print {
                    .btn { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="container mt-4">
                <h2 class="text-center mb-4">ملخص الشيفت</h2>
                ${content}
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    console.log('تحديث تلقائي للملخصات...');
    // يمكن إضافة تحديث AJAX هنا
}, 300000);
</script>

<?php include 'includes/footer.php'; ?>
