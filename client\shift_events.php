<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id'])) {
    if (!employeeCanAccessPage('shift_events')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    if (!employeeHasPermission('manage_shift_events') && !employeeHasPermission('view_shift_events')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$current_employee_id = $_SESSION['employee_id'] ?? null;

$page_title = "أحداث الشيفت";
$active_page = "shift_events";

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_event':
                    $stmt = $pdo->prepare("
                        INSERT INTO shift_events 
                        (shift_id, employee_id, event_type, event_title, event_description, 
                         severity, recorded_by, event_data) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $event_data = null;
                    if (!empty($_POST['event_data'])) {
                        $event_data = json_encode($_POST['event_data'], JSON_UNESCAPED_UNICODE);
                    }
                    
                    $stmt->execute([
                        $_POST['shift_id'],
                        $_POST['employee_id'] ?: null,
                        $_POST['event_type'],
                        $_POST['event_title'],
                        $_POST['event_description'] ?: null,
                        $_POST['severity'] ?: 'low',
                        $current_employee_id ?: $client_id,
                        $event_data
                    ]);
                    
                    $_SESSION['success'] = "تم إضافة الحدث بنجاح";
                    break;

                case 'resolve_event':
                    $stmt = $pdo->prepare("
                        UPDATE shift_events 
                        SET is_resolved = TRUE, resolved_at = CURRENT_TIMESTAMP, 
                            resolved_by = ?, resolution_notes = ?
                        WHERE event_id = ?
                    ");
                    $stmt->execute([
                        $current_employee_id ?: $client_id,
                        $_POST['resolution_notes'] ?: null,
                        $_POST['event_id']
                    ]);
                    
                    $_SESSION['success'] = "تم حل المشكلة بنجاح";
                    break;

                case 'delete_event':
                    $stmt = $pdo->prepare("DELETE FROM shift_events WHERE event_id = ?");
                    $stmt->execute([$_POST['event_id']]);
                    
                    $_SESSION['success'] = "تم حذف الحدث بنجاح";
                    break;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
    
    header('Location: shift_events.php');
    exit;
}

// الفلاتر
$shift_id = $_GET['shift_id'] ?? '';
$event_type = $_GET['event_type'] ?? '';
$severity = $_GET['severity'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$show_resolved = $_GET['show_resolved'] ?? '1';

// بناء الاستعلام
$where_conditions = ["s.client_id = ?"];
$params = [$client_id];

if ($shift_id) {
    $where_conditions[] = "se.shift_id = ?";
    $params[] = $shift_id;
}

if ($event_type) {
    $where_conditions[] = "se.event_type = ?";
    $params[] = $event_type;
}

if ($severity) {
    $where_conditions[] = "se.severity = ?";
    $params[] = $severity;
}

if ($date_from && $date_to) {
    $where_conditions[] = "DATE(se.recorded_at) BETWEEN ? AND ?";
    $params[] = $date_from;
    $params[] = $date_to;
}

if ($show_resolved === '0') {
    $where_conditions[] = "se.is_resolved = FALSE";
}

$where_clause = implode(' AND ', $where_conditions);

// جلب الأحداث
$events_query = "
    SELECT se.*, 
           s.shift_name, s.shift_date, s.start_time, s.end_time,
           e.name as employee_name, e.role as employee_role,
           recorder.name as recorded_by_name,
           resolver.name as resolved_by_name
    FROM shift_events se
    JOIN shifts s ON se.shift_id = s.shift_id
    LEFT JOIN employees e ON se.employee_id = e.id
    LEFT JOIN employees recorder ON se.recorded_by = recorder.id
    LEFT JOIN employees resolver ON se.resolved_by = resolver.id
    WHERE $where_clause
    ORDER BY se.recorded_at DESC
    LIMIT 100
";

$events = $pdo->prepare($events_query);
$events->execute($params);
$events_data = $events->fetchAll(PDO::FETCH_ASSOC);

// جلب الشيفتات للفلتر
$shifts = $pdo->prepare("
    SELECT shift_id, shift_name, shift_date 
    FROM shifts 
    WHERE client_id = ? AND shift_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ORDER BY shift_date DESC, start_time DESC
");
$shifts->execute([$client_id]);
$shifts_data = $shifts->fetchAll(PDO::FETCH_ASSOC);

// جلب الموظفين
$employees = $pdo->prepare("SELECT id, name, role FROM employees WHERE client_id = ? AND is_active = 1 ORDER BY name");
$employees->execute([$client_id]);
$employees_data = $employees->fetchAll(PDO::FETCH_ASSOC);

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_events,
        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_events,
        COUNT(CASE WHEN is_resolved = FALSE THEN 1 END) as unresolved_events,
        COUNT(CASE WHEN DATE(recorded_at) = CURDATE() THEN 1 END) as today_events
    FROM shift_events se
    JOIN shifts s ON se.shift_id = s.shift_id
    WHERE s.client_id = ? AND DATE(se.recorded_at) BETWEEN ? AND ?
";

$stats = $pdo->prepare($stats_query);
$stats->execute([$client_id, $date_from, $date_to]);
$stats_data = $stats->fetch(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-list-alt me-2"></i><?php echo $page_title; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if (isset($_SESSION['employee_id']) ? employeeHasPermission('manage_shift_events') : true): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEventModal">
                            <i class="fas fa-plus me-1"></i>إضافة حدث
                        </button>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['total_events']; ?></h4>
                            <p class="card-text">إجمالي الأحداث</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-danger">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['critical_events']; ?></h4>
                            <p class="card-text">أحداث حرجة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['unresolved_events']; ?></h4>
                            <p class="card-text">غير محلولة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body text-center">
                            <h4 class="card-title"><?php echo $stats_data['today_events']; ?></h4>
                            <p class="card-text">أحداث اليوم</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">فلاتر البحث</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="shift_id" class="form-label">الشيفت</label>
                            <select class="form-select" id="shift_id" name="shift_id">
                                <option value="">جميع الشيفتات</option>
                                <?php foreach ($shifts_data as $shift): ?>
                                    <option value="<?php echo $shift['shift_id']; ?>" <?php echo $shift_id == $shift['shift_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($shift['shift_name']); ?> - <?php echo $shift['shift_date']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="event_type" class="form-label">نوع الحدث</label>
                            <select class="form-select" id="event_type" name="event_type">
                                <option value="">جميع الأنواع</option>
                                <option value="shift_start" <?php echo $event_type == 'shift_start' ? 'selected' : ''; ?>>بداية الشيفت</option>
                                <option value="shift_end" <?php echo $event_type == 'shift_end' ? 'selected' : ''; ?>>نهاية الشيفت</option>
                                <option value="employee_checkin" <?php echo $event_type == 'employee_checkin' ? 'selected' : ''; ?>>حضور موظف</option>
                                <option value="employee_checkout" <?php echo $event_type == 'employee_checkout' ? 'selected' : ''; ?>>انصراف موظف</option>
                                <option value="break_start" <?php echo $event_type == 'break_start' ? 'selected' : ''; ?>>بداية استراحة</option>
                                <option value="break_end" <?php echo $event_type == 'break_end' ? 'selected' : ''; ?>>نهاية استراحة</option>
                                <option value="incident" <?php echo $event_type == 'incident' ? 'selected' : ''; ?>>حادث</option>
                                <option value="note" <?php echo $event_type == 'note' ? 'selected' : ''; ?>>ملاحظة</option>
                                <option value="issue_reported" <?php echo $event_type == 'issue_reported' ? 'selected' : ''; ?>>مشكلة مبلغة</option>
                                <option value="other" <?php echo $event_type == 'other' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="severity" class="form-label">الأهمية</label>
                            <select class="form-select" id="severity" name="severity">
                                <option value="">جميع المستويات</option>
                                <option value="low" <?php echo $severity == 'low' ? 'selected' : ''; ?>>منخفضة</option>
                                <option value="medium" <?php echo $severity == 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                                <option value="high" <?php echo $severity == 'high' ? 'selected' : ''; ?>>عالية</option>
                                <option value="critical" <?php echo $severity == 'critical' ? 'selected' : ''; ?>>حرجة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_resolved" name="show_resolved" value="1" <?php echo $show_resolved == '1' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="show_resolved">
                                    المحلولة
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="shift_events.php" class="btn btn-secondary">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الأحداث -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">سجل الأحداث</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($events_data)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> لا توجد أحداث للفترة المحددة
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الوقت</th>
                                        <th>الشيفت</th>
                                        <th>النوع</th>
                                        <th>العنوان</th>
                                        <th>الموظف</th>
                                        <th>الأهمية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($events_data as $event): ?>
                                    <tr>
                                        <td>
                                            <small><?php echo date('Y-m-d', strtotime($event['recorded_at'])); ?></small><br>
                                            <strong><?php echo date('H:i:s', strtotime($event['recorded_at'])); ?></strong>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($event['shift_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo $event['shift_date']; ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $type_labels = [
                                                'shift_start' => 'بداية الشيفت',
                                                'shift_end' => 'نهاية الشيفت',
                                                'employee_checkin' => 'حضور موظف',
                                                'employee_checkout' => 'انصراف موظف',
                                                'break_start' => 'بداية استراحة',
                                                'break_end' => 'نهاية استراحة',
                                                'incident' => 'حادث',
                                                'note' => 'ملاحظة',
                                                'issue_reported' => 'مشكلة مبلغة',
                                                'other' => 'أخرى'
                                            ];
                                            ?>
                                            <span class="badge bg-secondary"><?php echo $type_labels[$event['event_type']] ?? $event['event_type']; ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($event['event_title']); ?></strong>
                                            <?php if ($event['event_description']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars(substr($event['event_description'], 0, 100)); ?><?php echo strlen($event['event_description']) > 100 ? '...' : ''; ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($event['employee_name']): ?>
                                                <strong><?php echo htmlspecialchars($event['employee_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo $event['employee_role']; ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $severity_classes = [
                                                'low' => 'bg-success',
                                                'medium' => 'bg-warning',
                                                'high' => 'bg-danger',
                                                'critical' => 'bg-dark'
                                            ];
                                            $severity_labels = [
                                                'low' => 'منخفضة',
                                                'medium' => 'متوسطة',
                                                'high' => 'عالية',
                                                'critical' => 'حرجة'
                                            ];
                                            ?>
                                            <span class="badge <?php echo $severity_classes[$event['severity']] ?? 'bg-secondary'; ?>">
                                                <?php echo $severity_labels[$event['severity']] ?? $event['severity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($event['is_resolved']): ?>
                                                <span class="badge bg-success">محلول</span>
                                                <?php if ($event['resolved_at']): ?>
                                                    <br><small class="text-muted"><?php echo date('H:i', strtotime($event['resolved_at'])); ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-warning">غير محلول</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group-vertical btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="viewEvent(<?php echo $event['event_id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if (!$event['is_resolved'] && (isset($_SESSION['employee_id']) ? employeeHasPermission('manage_shift_events') : true)): ?>
                                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="resolveEvent(<?php echo $event['event_id']; ?>)">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <?php if (isset($_SESSION['employee_id']) ? employeeHasPermission('manage_shift_events') : true): ?>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteEvent(<?php echo $event['event_id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نموذج إضافة حدث جديد -->
<div class="modal fade" id="addEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة حدث جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_event">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shift_id_add" class="form-label">الشيفت *</label>
                                <select class="form-select" id="shift_id_add" name="shift_id" required>
                                    <option value="">-- اختر الشيفت --</option>
                                    <?php foreach ($shifts_data as $shift): ?>
                                        <option value="<?php echo $shift['shift_id']; ?>">
                                            <?php echo htmlspecialchars($shift['shift_name']); ?> - <?php echo $shift['shift_date']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employee_id_add" class="form-label">الموظف</label>
                                <select class="form-select" id="employee_id_add" name="employee_id">
                                    <option value="">-- اختر الموظف (اختياري) --</option>
                                    <?php foreach ($employees_data as $emp): ?>
                                        <option value="<?php echo $emp['id']; ?>">
                                            <?php echo htmlspecialchars($emp['name']); ?> (<?php echo $emp['role']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="event_type_add" class="form-label">نوع الحدث *</label>
                                <select class="form-select" id="event_type_add" name="event_type" required>
                                    <option value="">-- اختر النوع --</option>
                                    <option value="shift_start">بداية الشيفت</option>
                                    <option value="shift_end">نهاية الشيفت</option>
                                    <option value="shift_pause">إيقاف الشيفت</option>
                                    <option value="shift_resume">استئناف الشيفت</option>
                                    <option value="employee_checkin">حضور موظف</option>
                                    <option value="employee_checkout">انصراف موظف</option>
                                    <option value="break_start">بداية استراحة</option>
                                    <option value="break_end">نهاية استراحة</option>
                                    <option value="incident">حادث</option>
                                    <option value="note">ملاحظة</option>
                                    <option value="task_completed">مهمة مكتملة</option>
                                    <option value="issue_reported">مشكلة مبلغة</option>
                                    <option value="equipment_check">فحص معدات</option>
                                    <option value="customer_complaint">شكوى عميل</option>
                                    <option value="maintenance">صيانة</option>
                                    <option value="shift_handover">تسليم الشيفت</option>
                                    <option value="emergency">طوارئ</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="severity_add" class="form-label">مستوى الأهمية</label>
                                <select class="form-select" id="severity_add" name="severity">
                                    <option value="low">منخفضة</option>
                                    <option value="medium" selected>متوسطة</option>
                                    <option value="high">عالية</option>
                                    <option value="critical">حرجة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="event_title_add" class="form-label">عنوان الحدث *</label>
                        <input type="text" class="form-control" id="event_title_add" name="event_title" required maxlength="200">
                    </div>

                    <div class="mb-3">
                        <label for="event_description_add" class="form-label">وصف الحدث</label>
                        <textarea class="form-control" id="event_description_add" name="event_description" rows="4"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الحدث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج عرض تفاصيل الحدث -->
<div class="modal fade" id="viewEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الحدث</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="eventDetailsContent">
                <!-- سيتم تحميل المحتوى عبر AJAX -->
            </div>
        </div>
    </div>
</div>

<!-- نموذج حل المشكلة -->
<div class="modal fade" id="resolveEventModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حل المشكلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="resolveEventForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="resolve_event">
                    <input type="hidden" name="event_id" id="resolve_event_id">

                    <div class="mb-3">
                        <label for="resolution_notes" class="form-label">ملاحظات الحل</label>
                        <textarea class="form-control" id="resolution_notes" name="resolution_notes" rows="4" placeholder="اكتب تفاصيل كيفية حل المشكلة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">تأكيد الحل</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// بيانات الأحداث للجافا سكريبت
const eventsData = <?php echo json_encode($events_data, JSON_UNESCAPED_UNICODE); ?>;

// عرض تفاصيل الحدث
function viewEvent(eventId) {
    const event = eventsData.find(e => e.event_id == eventId);
    if (!event) return;

    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات أساسية</h6>
                <table class="table table-sm">
                    <tr><td><strong>الوقت:</strong></td><td>${event.recorded_at}</td></tr>
                    <tr><td><strong>الشيفت:</strong></td><td>${event.shift_name} - ${event.shift_date}</td></tr>
                    <tr><td><strong>النوع:</strong></td><td>${event.event_type}</td></tr>
                    <tr><td><strong>الأهمية:</strong></td><td>${event.severity}</td></tr>
                    <tr><td><strong>الموظف:</strong></td><td>${event.employee_name || 'غير محدد'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>تفاصيل إضافية</h6>
                <table class="table table-sm">
                    <tr><td><strong>مسجل بواسطة:</strong></td><td>${event.recorded_by_name || 'غير محدد'}</td></tr>
                    <tr><td><strong>الحالة:</strong></td><td>${event.is_resolved ? 'محلول' : 'غير محلول'}</td></tr>
                    ${event.resolved_at ? `<tr><td><strong>تاريخ الحل:</strong></td><td>${event.resolved_at}</td></tr>` : ''}
                    ${event.resolved_by_name ? `<tr><td><strong>حل بواسطة:</strong></td><td>${event.resolved_by_name}</td></tr>` : ''}
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>العنوان</h6>
                <p class="border p-2">${event.event_title}</p>
            </div>
        </div>
        ${event.event_description ? `
        <div class="row">
            <div class="col-12">
                <h6>الوصف</h6>
                <p class="border p-2">${event.event_description}</p>
            </div>
        </div>
        ` : ''}
        ${event.resolution_notes ? `
        <div class="row">
            <div class="col-12">
                <h6>ملاحظات الحل</h6>
                <p class="border p-2 bg-light">${event.resolution_notes}</p>
            </div>
        </div>
        ` : ''}
    `;

    document.getElementById('eventDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('viewEventModal')).show();
}

// حل المشكلة
function resolveEvent(eventId) {
    document.getElementById('resolve_event_id').value = eventId;
    new bootstrap.Modal(document.getElementById('resolveEventModal')).show();
}

// حذف الحدث
function deleteEvent(eventId) {
    if (confirm('هل أنت متأكد من حذف هذا الحدث؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_event">
            <input type="hidden" name="event_id" value="${eventId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// تحديث تلقائي كل دقيقة
setInterval(function() {
    // يمكن إضافة تحديث AJAX هنا
    console.log('تحديث تلقائي للأحداث...');
}, 60000);
</script>

<?php include 'includes/footer.php'; ?>
