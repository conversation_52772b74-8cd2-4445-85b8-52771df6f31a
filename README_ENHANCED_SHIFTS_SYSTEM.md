# النظام المحسن لإدارة الشيفتات - PlayGood

تم تطوير نظام شامل ومتقدم لإدارة الشيفتات مع تسجيل مفصل لجميع العمليات وإنشاء تقارير تلقائية للإدمن.

## 🌟 الميزات الجديدة

### 1. نظام تسجيل الأحداث المفصل
- **تسجيل تلقائي** لجميع أحداث الشيفت (حضور، انصراف، استراحات)
- **تسجيل يدوي** للأحداث الخاصة (حوادث، ملاحظات، مشاكل)
- **تصنيف الأحداث** حسب الأهمية (منخفضة، متوسطة، عالية، حرجة)
- **ربط الأحداث** بالموظفين والشيفتات
- **تتبع حالة الأحداث** (محلول/غير محلول)

### 2. نظام التقارير التلقائية
- **إنشاء تلقائي** لملخصات الشيفت عند الانتهاء
- **حساب تلقائي** للإحصائيات (حضور، ساعات عمل، أداء)
- **تقييم أداء الموظفين** في كل شيفت
- **تصدير التقارير** بصيغ مختلفة (HTML, CSV)
- **طباعة التقارير** مع تنسيق احترافي

### 3. نظام الإشعارات الذكي
- **إشعارات تلقائية** عند اكتمال الشيفتات
- **تنبيهات فورية** للأحداث الحرجة
- **إشعارات المتصفح** مع الصوت
- **تصنيف الإشعارات** حسب الأولوية
- **إدارة متقدمة** للإشعارات (قراءة، رفض، حذف)

### 4. نظام تقييم الأداء
- **تقييم تلقائي** بناءً على الحضور والالتزام
- **نقاط الأداء** من 10 لكل موظف
- **تتبع التحسن** عبر الوقت
- **تقارير الأداء** الشخصية والجماعية

### 5. نظام إدارة المهام
- **تخصيص مهام** لكل شيفت
- **تتبع إنجاز المهام** مع الوقت المستغرق
- **تقييم كفاءة** إنجاز المهام
- **ملاحظات التكميل** لكل مهمة

## 📁 الملفات المضافة

### قاعدة البيانات
- `enhance_shifts_logging_system.sql` - سكريبت التحسينات الشامل
- `setup_enhanced_shifts_system.php` - ملف التشغيل والتحقق

### الصفحات الجديدة
- `client/shift_events.php` - إدارة أحداث الشيفت
- `client/shift_summaries.php` - عرض ملخصات الشيفت
- `client/notifications.php` - إدارة الإشعارات

### ملفات API
- `client/api/get_shift_summary.php` - جلب تفاصيل ملخص الشيفت
- `client/api/export_shift_summary.php` - تصدير ملخصات الشيفت
- `client/api/check_notifications.php` - فحص الإشعارات الجديدة

### ملفات JavaScript
- `client/assets/js/notifications.js` - نظام الإشعارات التفاعلي

## 🗄️ الجداول الجديدة

### 1. shift_events
تسجيل جميع أحداث الشيفت مع التفاصيل والأوقات
```sql
- event_id: معرف الحدث
- shift_id: معرف الشيفت
- employee_id: معرف الموظف (اختياري)
- event_type: نوع الحدث
- event_title: عنوان الحدث
- event_description: وصف مفصل
- severity: مستوى الأهمية
- is_resolved: هل تم حل المشكلة
- recorded_at: وقت التسجيل
```

### 2. shift_summaries
ملخصات شاملة لكل شيفت مع الإحصائيات
```sql
- summary_id: معرف الملخص
- shift_id: معرف الشيفت
- total_employees_assigned: عدد الموظفين المخصصين
- total_employees_attended: عدد الموظفين الحاضرين
- total_work_hours: إجمالي ساعات العمل
- attendance_percentage: نسبة الحضور
- performance_score: نقاط الأداء العام
- total_events: عدد الأحداث
- critical_events: عدد الأحداث الحرجة
```

### 3. shift_employee_performance
تقييم أداء كل موظف في كل شيفت
```sql
- performance_id: معرف التقييم
- shift_id: معرف الشيفت
- employee_id: معرف الموظف
- attendance_score: نقاط الحضور
- punctuality_score: نقاط الالتزام بالوقت
- performance_score: نقاط الأداء العام
- total_score: المجموع الكلي
```

### 4. admin_notifications
نظام الإشعارات للإدمن
```sql
- notification_id: معرف الإشعار
- client_id: معرف العميل
- notification_type: نوع الإشعار
- title: عنوان الإشعار
- message: نص الإشعار
- priority: مستوى الأولوية
- is_read: هل تم قراءته
- action_url: رابط الإجراء
```

### 5. shift_tasks
إدارة مهام الشيفت
```sql
- task_id: معرف المهمة
- shift_id: معرف الشيفت
- task_title: عنوان المهمة
- assigned_to: المخصص له
- task_status: حالة المهمة
- estimated_duration: المدة المتوقعة
- actual_duration: المدة الفعلية
```

## ⚙️ المشغلات التلقائية (Triggers)

### 1. shift_attendance_events
تسجيل تلقائي لأحداث الحضور والانصراف

### 2. shift_completion_notification
إشعار تلقائي عند اكتمال الشيفت

### 3. shift_summary_notification
إشعار عند إنشاء ملخص الشيفت

### 4. critical_event_notification
إشعار فوري للأحداث الحرجة

## 🔧 الإجراءات المخزنة (Stored Procedures)

### GenerateShiftSummary
إنشاء ملخص شامل للشيفت مع جميع الإحصائيات

## 👁️ العروض (Views)

### shift_detailed_view
عرض شامل لتفاصيل الشيفت مع الإحصائيات

## 🚀 طريقة التشغيل

### 1. تشغيل النظام
```bash
# افتح المتصفح وانتقل إلى:
http://localhost/playgood/setup_enhanced_shifts_system.php
```

### 2. التحقق من التشغيل
- تأكد من إنشاء جميع الجداول
- تحقق من وجود الصلاحيات الجديدة
- اختبر الصفحات الجديدة

### 3. إعداد الصلاحيات
- امنح الموظفين الصلاحيات المناسبة
- تأكد من إمكانية الوصول للصفحات الجديدة

## 📋 دليل الاستخدام

### للمديرين

#### 1. إنشاء شيفت جديد
1. انتقل إلى صفحة الورديات
2. أنشئ شيفت جديد مع تحديد الأوقات
3. خصص الموظفين للشيفت
4. حدد مشرف الشيفت

#### 2. متابعة الشيفت
1. راقب الحضور من صفحة الحضور والانصراف
2. تابع الأحداث من صفحة أحداث الشيفت
3. تحقق من الإشعارات بانتظام

#### 3. إنهاء الشيفت
1. غير حالة الشيفت إلى "مكتمل"
2. انتقل إلى صفحة ملخصات الشيفت
3. أنشئ ملخص تلقائي للشيفت
4. راجع التقرير وأضف ملاحظاتك

### للموظفين

#### 1. تسجيل الحضور
- استخدم صفحة الحضور والانصراف
- سجل بداية ونهاية الاستراحات

#### 2. تسجيل الأحداث
- سجل أي أحداث مهمة في صفحة أحداث الشيفت
- حدد مستوى الأهمية المناسب

#### 3. إنجاز المهام
- تابع المهام المخصصة لك
- سجل وقت إنجاز كل مهمة

## 🔔 نظام الإشعارات

### أنواع الإشعارات
- **اكتمال شيفت**: عند انتهاء أي شيفت
- **تقرير جاهز**: عند إنشاء ملخص جديد
- **حدث حرج**: للأحداث عالية الأهمية
- **مشاكل غير محلولة**: تذكير بالمشاكل المعلقة

### إعدادات الإشعارات
- إشعارات المتصفح مع الصوت
- إشعارات داخل الصفحة
- تحديث تلقائي كل 30 ثانية
- إمكانية تخصيص الإعدادات

## 📊 التقارير والإحصائيات

### تقارير الشيفت
- ملخص شامل لكل شيفت
- إحصائيات الحضور والأداء
- تفاصيل الأحداث والمهام
- تقييم الموظفين

### تقارير الأداء
- نقاط الأداء لكل موظف
- مقارنة الأداء عبر الوقت
- إحصائيات الحضور والالتزام
- تقارير شهرية وسنوية

### تصدير التقارير
- تصدير HTML للطباعة
- تصدير CSV للتحليل
- إمكانية الطباعة المباشرة
- حفظ التقارير كملفات

## 🛡️ الأمان والصلاحيات

### صلاحيات جديدة
- `manage_shift_events`: إدارة أحداث الشيفت
- `view_shift_summaries`: عرض ملخصات الشيفت
- `evaluate_employees`: تقييم الموظفين
- `manage_shift_tasks`: إدارة مهام الشيفت
- `generate_shift_reports`: إنشاء تقارير الشيفت

### حماية البيانات
- تشفير البيانات الحساسة
- تسجيل جميع العمليات
- نسخ احتياطية تلقائية
- مراقبة الوصول غير المصرح

## 🔧 الصيانة والدعم

### النسخ الاحتياطية
- نسخ احتياطية يومية تلقائية
- حفظ البيانات لمدة 90 يوم
- إمكانية الاستعادة السريعة

### مراقبة الأداء
- مراقبة استخدام قاعدة البيانات
- تحسين الاستعلامات تلقائياً
- تنظيف البيانات القديمة

### التحديثات
- تحديثات أمنية دورية
- إضافة ميزات جديدة
- تحسين الأداء المستمر

## 📞 الدعم الفني

للحصول على الدعم أو الإبلاغ عن مشاكل:
1. تحقق من سجلات الأخطاء في PHP
2. راجع صلاحيات قاعدة البيانات
3. تأكد من تشغيل جميع الخدمات المطلوبة

---

**تم تطوير هذا النظام بعناية فائقة ليوفر حلاً شاملاً ومتقدماً لإدارة الشيفتات مع تسجيل مفصل لجميع العمليات وإنشاء تقارير تلقائية احترافية للإدمن.**
